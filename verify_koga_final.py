#!/usr/bin/env python3
"""
Verificação final: <PERSON><PERSON><PERSON>r que Koga não tem mais <PERSON>
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def verify_koga_final():
    """Verifica a party final de Koga na ROM"""
    
    print("🔍 VERIFICAÇÃO FINAL: Party de Koga na ROM")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable, LoadProjectPokemonDatabase
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # Mapeamento de tipos
        type_names = {
            0: "NORMAL", 1: "FIGHTING", 2: "FLYING", 3: "POISON", 4: "GROUND",
            5: "ROCK", 6: "BUG", 7: "GHOST", 8: "STEEL", 9: "FIRE",
            10: "WATER", 11: "GRASS", 12: "ELECTRIC", 13: "PSYCH<PERSON>", 14: "ICE",
            15: "DRAGON", 16: "DARK", 17: "FAIRY", 18: "ROOSTLESS", 19: "BLANK"
        }
        
        # Verificar Koga na ROM atual
        koga_id = 418
        
        print(f"🎯 VERIFICAÇÃO DE KOGA (ID {koga_id}):")
        
        with open("test.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Party size: {party_size}")
            print(f"   Party offset: 0x{party_offset:08X}")
            
            # Ler party atual
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n📋 PARTY ATUAL DE KOGA:")
            ponyta_found = False
            poison_count = 0
            
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                # Verificar dados do projeto
                if species_id in project_pokemon_data:
                    pokemon_data_proj = project_pokemon_data[species_id]
                    name = pokemon_data_proj.get('name', f'Species_{species_id}')
                    type1 = pokemon_data_proj.get('type1')
                    type2 = pokemon_data_proj.get('type2')
                    
                    type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                    type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                    
                    type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                    
                    # Verificar se é Ponyta
                    if species_id == 77:
                        ponyta_found = True
                        print(f"   Slot {i+1}: 🚨 PONYTA ENCONTRADO! #{species_id} {name} Level {level} ({type_str})")
                    else:
                        print(f"   Slot {i+1}: #{species_id:4d} {name:15s} Level {level:2d} ({type_str})")
                    
                    # Contar Pokémon POISON
                    if type1 == 3 or type2 == 3:  # POISON = 3
                        poison_count += 1
                        print(f"      ✅ POISON type - adequado para Koga")
                    elif type1 == 9 or type2 == 9:  # FIRE = 9
                        print(f"      🚨 FIRE type - NÃO adequado para Koga!")
                else:
                    print(f"   Slot {i+1}: #{species_id:4d} {'Unknown':15s} Level {level:2d} (Não encontrado no projeto)")
                    
                    # Verificar se é Ponyta mesmo sem dados do projeto
                    if species_id == 77:
                        ponyta_found = True
                        print(f"      🚨 PONYTA ENCONTRADO! (Species #77)")
            
            print(f"\n📊 ANÁLISE FINAL:")
            print(f"   🐴 Ponyta encontrado: {'SIM' if ponyta_found else 'NÃO'}")
            print(f"   🐍 Pokémon POISON: {poison_count}/{party_size}")
            
            if ponyta_found:
                print(f"   ❌ PROBLEMA AINDA EXISTE: Ponyta ainda está na party!")
                print(f"   🔍 Isso indica que há outra função sobrescrevendo as correções")
            else:
                print(f"   ✅ PROBLEMA RESOLVIDO: Ponyta não está mais na party!")
                print(f"   🎯 Sistema agora usa dados do projeto corretamente")
            
            if poison_count >= 2:
                print(f"   ✅ TEMA CORRETO: Koga tem {poison_count} Pokémon POISON")
            else:
                print(f"   ⚠️ TEMA PARCIAL: Koga tem apenas {poison_count} Pokémon POISON")
        
        return not ponyta_found
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_expected_vs_actual():
    """Compara o que era esperado vs o que foi encontrado"""
    
    print(f"\n📊 COMPARAÇÃO: Esperado vs Real")
    print("=" * 40)
    
    print(f"🎯 BASEADO NOS LOGS DE COMPILAÇÃO:")
    print(f"   Esperado para Koga:")
    print(f"      Slot 5: #23 (Ekans - POISON)")
    print(f"      Slot 6: #24 (Arbok - POISON)")
    print(f"   Log final: 🏆 KOGA: Added #3, #643")
    
    print(f"\n🔍 DISCREPÂNCIA NOS LOGS:")
    print(f"   ❓ Logs de inserção mostram #23, #24")
    print(f"   ❓ Log final mostra #3, #643")
    print(f"   🔍 Isso pode indicar múltiplas passadas de modificação")
    
    print(f"\n💡 EXPLICAÇÃO POSSÍVEL:")
    print(f"   1. Primeira passada: Insere #23, #24 (Ekans, Arbok)")
    print(f"   2. Segunda passada: Substitui por #3, #643 (outros POISON)")
    print(f"   3. ROM final: Contém resultado da última passada")
    
    return True

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar verificação
    success = verify_koga_final()
    compare_expected_vs_actual()
    
    if success:
        print(f"\n🎉 SUCESSO! Problema resolvido!")
    else:
        print(f"\n⚠️ Problema ainda existe - investigação adicional necessária")
