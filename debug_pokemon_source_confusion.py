#!/usr/bin/env python3
"""
Debug: Investigar se o sistema está buscando Pokémon da ROM em vez do projeto
"""

import sys
import os

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_pokemon_source_confusion():
    """Investiga de onde o sistema está buscando os Pokémon"""
    
    print("🔍 DEBUG: De onde o sistema está buscando Pokémon?")
    print("=" * 60)
    
    try:
        # Verificar todas as funções que fazem seleção de Pokémon
        print("🎯 FUNÇÕES DE SELEÇÃO DE POKÉMON IDENTIFICADAS:")
        print("-" * 60)
        
        # 1. SelectAdditionalPokemonWithAppropriateTypes
        print("1. 📋 SelectAdditionalPokemonWithAppropriateTypes")
        print("   - Deveria usar banco de dados do projeto")
        print("   - Logs mostram que seleciona Species #1")
        
        # 2. ExpandTrainerPartyWithCachedData  
        print("\n2. 📋 ExpandTrainerPartyWithCachedData")
        print("   - Usa SelectAdditionalPokemonWithAppropriateTypes")
        print("   - Logs mostram inserção de #1")
        
        # 3. Outras funções que podem interferir
        print("\n3. 📋 POSSÍVEIS FUNÇÕES INTERFERENTES:")
        print("   - RandomizeAllTrainerPokemonWithCache")
        print("   - RandomizeTrainerPokemon")
        print("   - Outras funções de randomização")
        
        print(f"\n🚨 PROBLEMA IDENTIFICADO:")
        print(f"   ✅ Logs mostram seleção correta (Species #1 POISON)")
        print(f"   ✅ ROM mostra inserção diferente (Species #1360 STEEL/POISON)")
        print(f"   ❌ JOGO mostra Ponyta (#77 FIRE)")
        print(f"   🔍 Isso indica que HÁ OUTRA FUNÇÃO modificando após a inserção!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_rom_based_selection():
    """Procura funções que podem estar usando dados da ROM em vez do projeto"""
    
    print(f"\n🔍 PROCURANDO FUNÇÕES QUE USAM ROM EM VEZ DO PROJETO:")
    print("=" * 60)
    
    try:
        # Verificar o arquivo insert.py para funções suspeitas
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Procurar por funções que leem da ROM para seleção
        suspicious_patterns = [
            "rom.seek",
            "pokemon_stats_offset", 
            "0x254784",
            "original_rom",
            "BPRE0.gba"
        ]
        
        print(f"🔍 PADRÕES SUSPEITOS ENCONTRADOS:")
        
        for i, line in enumerate(lines):
            for pattern in suspicious_patterns:
                if pattern in line and ("def " in line or "pokemon" in line.lower() or "species" in line.lower()):
                    print(f"   Linha {i+1:4d}: {line.strip()}")
        
        # Procurar especificamente por SelectAdditionalPokemonWithAppropriateTypes
        print(f"\n🎯 ANÁLISE DE SelectAdditionalPokemonWithAppropriateTypes:")
        
        in_function = False
        function_lines = []
        
        for i, line in enumerate(lines):
            if "def SelectAdditionalPokemonWithAppropriateTypes" in line:
                in_function = True
                function_lines = []
            
            if in_function:
                function_lines.append((i+1, line))
                
                # Verificar se usa ROM ou projeto
                if "rom.seek" in line or "pokemon_stats_offset" in line:
                    print(f"   🚨 LINHA {i+1}: USA ROM - {line.strip()}")
                elif "project_pokemon_data" in line or "LoadProjectPokemonDatabase" in line:
                    print(f"   ✅ LINHA {i+1}: USA PROJETO - {line.strip()}")
                
                # Parar quando sair da função
                if line.strip() and not line.startswith(' ') and not line.startswith('\t') and i > 0:
                    if in_function and "def " in line:
                        break
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante busca: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_multiple_randomization_passes():
    """Verifica se há múltiplas passadas de randomização"""
    
    print(f"\n🔍 VERIFICANDO MÚLTIPLAS PASSADAS DE RANDOMIZAÇÃO:")
    print("=" * 60)
    
    try:
        print(f"🎯 HIPÓTESE: Sistema faz múltiplas passadas")
        print(f"   1. 🔄 PASSADA 1: ExpandTrainerPartyWithCachedData (adiciona Species #1)")
        print(f"   2. 🔄 PASSADA 2: RandomizeAllTrainerPokemonWithCache (randomiza TODOS)")
        print(f"   3. 🔄 PASSADA 3: Outra função (sobrescreve com Ponyta)")
        
        print(f"\n📋 EVIDÊNCIAS:")
        print(f"   ✅ Logs mostram 'Added #167, #643' (final)")
        print(f"   ✅ ROM mostra #643, #1360 (intermediário)")
        print(f"   ❌ JOGO mostra Ponyta (final real)")
        
        print(f"\n🔍 CONFIGURAÇÃO SUSPEITA:")
        print(f"   RANDOMIZE_ONLY_ADDITIONAL_POKEMON = True")
        print(f"   🚨 MAS pode haver função que ignora essa configuração!")
        
        # Verificar se há função que randomiza TODOS os Pokémon
        print(f"\n🧪 FUNÇÕES QUE PODEM RANDOMIZAR TODOS:")
        print(f"   - RandomizeAllTrainerPokemonWithCache")
        print(f"   - RandomizeTrainerPokemon") 
        print(f"   - Outras funções de randomização geral")
        
        print(f"\n💡 SOLUÇÃO SUGERIDA:")
        print(f"   1. Adicionar logs em TODAS as funções que modificam trainers")
        print(f"   2. Verificar ordem de execução das funções")
        print(f"   3. Verificar se RANDOMIZE_ONLY_ADDITIONAL_POKEMON é respeitado")
        print(f"   4. Verificar se há função final que sobrescreve tudo")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_randomization_workflow():
    """Analisa o workflow completo de randomização"""
    
    print(f"\n🔍 ANÁLISE DO WORKFLOW DE RANDOMIZAÇÃO:")
    print("=" * 60)
    
    try:
        print(f"📋 WORKFLOW ESPERADO:")
        print(f"   1. ✅ Ler dados originais da ROM (BPRE0.gba)")
        print(f"   2. ✅ Expandir parties (adicionar slots)")
        print(f"   3. ✅ Selecionar Pokémon do PROJETO para slots adicionais")
        print(f"   4. ❌ RANDOMIZAR apenas slots adicionais (se RANDOMIZE_ONLY_ADDITIONAL_POKEMON=True)")
        print(f"   5. ✅ Escrever na ROM final (test.gba)")
        
        print(f"\n🚨 WORKFLOW REAL (SUSPEITO):")
        print(f"   1. ✅ Ler dados originais da ROM (BPRE0.gba)")
        print(f"   2. ✅ Expandir parties (adicionar slots)")
        print(f"   3. ✅ Selecionar Pokémon do PROJETO para slots adicionais")
        print(f"   4. 🚨 RANDOMIZAR TODOS os Pokémon (ignorando configuração)")
        print(f"   5. 🚨 Usar dados da ROM ORIGINAL para randomização")
        print(f"   6. ✅ Escrever na ROM final (test.gba)")
        
        print(f"\n🎯 PONTOS DE FALHA IDENTIFICADOS:")
        print(f"   A. 🚨 Função de randomização usa ROM em vez do PROJETO")
        print(f"   B. 🚨 RANDOMIZE_ONLY_ADDITIONAL_POKEMON não é respeitado")
        print(f"   C. 🚨 Múltiplas funções modificam o mesmo trainer")
        print(f"   D. 🚨 Ordem de execução incorreta")
        
        print(f"\n🔧 CORREÇÕES NECESSÁRIAS:")
        print(f"   1. Garantir que randomização use APENAS dados do PROJETO")
        print(f"   2. Respeitar RANDOMIZE_ONLY_ADDITIONAL_POKEMON=True")
        print(f"   3. Evitar múltiplas modificações do mesmo trainer")
        print(f"   4. Adicionar logs detalhados em TODAS as funções")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_pokemon_source_confusion()
    find_rom_based_selection()
    check_multiple_randomization_passes()
    analyze_randomization_workflow()
