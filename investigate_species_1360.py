#!/usr/bin/env python3
"""
Investigar o que é Species #1360 que Koga recebeu
"""

import sys
import os

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def investigate_species_1360():
    """Investiga o que é Species #1360"""
    
    print("🔍 INVESTIGAÇÃO: O que é Species #1360?")
    print("=" * 50)
    
    try:
        from insert import LoadProjectPokemonDatabase
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        species_1360 = 1360
        
        print(f"🎯 ANÁLISE DE SPECIES #{species_1360}:")
        
        if species_1360 in project_pokemon_data:
            pokemon_data = project_pokemon_data[species_1360]
            name = pokemon_data.get('name', f'Species_{species_1360}')
            type1 = pokemon_data.get('type1')
            type2 = pokemon_data.get('type2')
            is_legendary = pokemon_data.get('is_legendary', False)
            
            type_names = {
                0: "NORMAL", 1: "FIGHTING", 2: "FLYING", 3: "POISON", 4: "GROUND",
                5: "ROCK", 6: "BUG", 7: "GHOST", 8: "STEEL", 9: "FIRE",
                10: "WATER", 11: "GRASS", 12: "ELECTRIC", 13: "PSYCHIC", 14: "ICE",
                15: "DRAGON", 16: "DARK", 17: "FAIRY", 18: "ROOSTLESS", 19: "BLANK"
            }
            
            type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
            type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
            
            type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
            legendary_str = " ⭐ LEGENDARY" if is_legendary else ""
            
            print(f"   ✅ ENCONTRADO!")
            print(f"   Nome: {name}")
            print(f"   Tipos: {type_str}")
            print(f"   Type1: {type1} ({type1_name})")
            print(f"   Type2: {type2} ({type2_name if type2_name else 'None'})")
            print(f"   Legendary: {is_legendary}{legendary_str}")
            
            # Verificar se combina com Koga (POISON/GHOST)
            koga_types = [3, 7]  # POISON, GHOST
            matches = []
            if type1 in koga_types:
                matches.append(type1_name)
            if type2 is not None and type2 in koga_types:
                matches.append(type2_name)
            
            if matches:
                print(f"   ✅ COMBINA com Koga: {', '.join(matches)}")
            else:
                print(f"   ❌ NÃO COMBINA com Koga (POISON/GHOST)")
            
            # Verificar se pode ser Ponyta
            if 'ponyta' in name.lower() or 'pony' in name.lower():
                print(f"   🐴 PODE SER PONYTA ou variante!")
            elif type1 == 9 or type2 == 9:  # FIRE
                print(f"   🔥 É FIRE TYPE - pode ser relacionado a Ponyta")
            
        else:
            print(f"   ❌ Species #{species_1360} NÃO ENCONTRADO no banco de dados")
            print(f"   🔍 Isso pode indicar:")
            print(f"      - Species ID inválido")
            print(f"      - Pokémon não carregado no banco")
            print(f"      - Erro na leitura da ROM")
        
        # Verificar range de Species IDs no projeto
        print(f"\n📊 ANÁLISE DO RANGE DE SPECIES:")
        
        min_species = min(project_pokemon_data.keys()) if project_pokemon_data else 0
        max_species = max(project_pokemon_data.keys()) if project_pokemon_data else 0
        total_species = len(project_pokemon_data)
        
        print(f"   Range: #{min_species} - #{max_species}")
        print(f"   Total: {total_species} Pokémon")
        print(f"   Species #1360 está {'DENTRO' if min_species <= 1360 <= max_species else 'FORA'} do range")
        
        # Verificar Pokémon próximos a 1360
        print(f"\n🔍 POKÉMON PRÓXIMOS A #{species_1360}:")
        
        for offset in range(-5, 6):
            check_species = species_1360 + offset
            if check_species in project_pokemon_data:
                pokemon_data = project_pokemon_data[check_species]
                name = pokemon_data.get('name', f'Species_{check_species}')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                
                type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                marker = ">>> " if offset == 0 else "    "
                
                print(f"{marker}#{check_species:4d}: {name:20s} ({type_str})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante investigação: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_species_mapping():
    """Verifica se há mapeamento de Species IDs"""
    
    print(f"\n🔍 VERIFICAÇÃO: Mapeamento de Species IDs")
    print("=" * 50)
    
    try:
        print(f"🎯 HIPÓTESE: Transformação #1 → #1360")
        
        # Verificar se há padrão matemático
        original = 1
        final = 1360
        difference = final - original
        
        print(f"   Original: #{original}")
        print(f"   Final: #{final}")
        print(f"   Diferença: +{difference}")
        print(f"   Hexadecimal: 0x{original:04X} → 0x{final:04X}")
        
        # Verificar se 1359 é um offset comum
        print(f"\n🔍 ANÁLISE DO OFFSET +1359:")
        print(f"   1359 em hex: 0x{1359:04X}")
        print(f"   1359 em binário: {bin(1359)}")
        
        # Verificar se há outros casos similares
        print(f"\n🧪 TESTE: Outros Species com mesmo padrão")
        
        test_species = [1, 2, 3, 77, 167]  # Alguns Species importantes
        
        from insert import LoadProjectPokemonDatabase
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        for species in test_species:
            mapped_species = species + 1359
            
            original_exists = species in project_pokemon_data
            mapped_exists = mapped_species in project_pokemon_data
            
            original_name = project_pokemon_data.get(species, {}).get('name', 'N/A') if original_exists else 'N/A'
            mapped_name = project_pokemon_data.get(mapped_species, {}).get('name', 'N/A') if mapped_exists else 'N/A'
            
            print(f"   #{species:3d} ({original_name:15s}) → #{mapped_species:4d} ({mapped_name:15s}) | Original: {'✅' if original_exists else '❌'} | Mapped: {'✅' if mapped_exists else '❌'}")
        
        # Verificar se Ponyta tem mapeamento
        ponyta_original = 77
        ponyta_mapped = ponyta_original + 1359  # 1436
        
        print(f"\n🐴 TESTE ESPECÍFICO: Ponyta")
        print(f"   Ponyta original: #{ponyta_original}")
        print(f"   Ponyta mapeado: #{ponyta_mapped}")
        
        if ponyta_mapped in project_pokemon_data:
            ponyta_data = project_pokemon_data[ponyta_mapped]
            ponyta_name = ponyta_data.get('name', f'Species_{ponyta_mapped}')
            print(f"   ✅ Species #{ponyta_mapped} existe: {ponyta_name}")
            
            if 'ponyta' in ponyta_name.lower():
                print(f"   🎯 PODE SER PONYTA MAPEADO!")
        else:
            print(f"   ❌ Species #{ponyta_mapped} não existe")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar investigação
    investigate_species_1360()
    check_species_mapping()
