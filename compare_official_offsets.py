#!/usr/bin/env python3
"""
Comparação com offsets oficiais do Fire Red
Verificar se estamos usando os dados corretos
"""

import sys
import struct
sys.path.append('scripts')

def compare_official_offsets():
    """Compara nossos offsets com os oficiais"""
    
    print("🔍 COMPARAÇÃO: OFFSETS OFICIAIS vs NOSSOS")
    print("=" * 60)
    
    # Offsets oficiais do Fire Red (U) 1.0 do arquivo .offsets/gen3_offsets.ini
    official_offsets = {
        "TrainerData": 0x23EAC8,
        "TrainerClassNames": 0x23E558,
        "PokemonStats": 0x254784,
        "PokemonMovesets": 0x25D7B4,
        "PokemonEvolutions": 0x259754,
        "StarterPokemon": 0x169B69,
        "MoveData": 0x1FB12C,
        "ItemData": 0x3C55BC,
        "WildPokemon": None,  # Calculado dinamicamente
    }
    
    # Nossos offsets atuais
    our_offsets = {
        "TrainerData": 0x23EAC8,  # Mesmo que usamos
        "PokemonStats": 0x254784,  # Mesmo que usamos
    }
    
    print("📊 COMPARAÇÃO DE OFFSETS:")
    print("-" * 40)
    
    for key in official_offsets:
        official = official_offsets[key]
        our = our_offsets.get(key, "Not used")
        
        if official is None:
            status = "🔍 Dynamic"
        elif our == "Not used":
            status = "❓ Not used"
        elif our == official:
            status = "✅ MATCH"
        else:
            status = "❌ DIFFERENT"
        
        print(f"  {key}:")
        print(f"    Official: 0x{official:08X}" if official else "    Official: Dynamic")
        print(f"    Our:      {our if isinstance(our, str) else f'0x{our:08X}'}")
        print(f"    Status:   {status}")
        print()
    
    # Verificar se nossa ROM é realmente Fire Red (U) 1.0
    print("🔍 VERIFICAÇÃO DA ROM:")
    print("-" * 40)
    
    try:
        with open('BPRE0.gba', 'rb') as rom:
            # Verificar header
            rom.seek(0xA0)
            game_code = rom.read(4).decode('ascii', errors='ignore')
            
            rom.seek(0xAC)
            game_title = rom.read(12).decode('ascii', errors='ignore').strip('\x00')
            
            rom.seek(0xBC)
            version = rom.read(1)[0]
            
            print(f"  Game Code: {game_code}")
            print(f"  Game Title: {game_title}")
            print(f"  Version: {version}")
            
            # Verificar CRC32 se possível
            expected_crc32 = "DD88761C"  # Fire Red (U) 1.0 CRC32
            print(f"  Expected CRC32: {expected_crc32}")
            
            if game_code == "BPRE" and version == 0:
                print("  ✅ ROM identificada como Fire Red (U) 1.0")
            else:
                print("  ⚠️  ROM pode não ser Fire Red (U) 1.0 padrão")
            
    except FileNotFoundError:
        print("  ❌ ROM BPRE0.gba não encontrada")
    
    # Verificar estrutura de dados do trainer
    print("\n🔍 VERIFICAÇÃO DA ESTRUTURA DE TRAINER:")
    print("-" * 40)
    
    try:
        with open('BPRE0.gba', 'rb') as rom:
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40  # Tamanho padrão
            
            # Testar diferentes tamanhos de entrada
            for test_size in [32, 36, 40, 44, 48]:
                koga_offset = trainer_table_offset + (koga_trainer_id * test_size)
                
                rom.seek(koga_offset)
                trainer_data = rom.read(48)  # Ler mais dados para análise
                
                if len(trainer_data) >= test_size:
                    # Tentar interpretar como estrutura de trainer
                    if test_size >= 40:
                        trainer_class = trainer_data[1] if len(trainer_data) > 1 else 0
                        party_size = trainer_data[32] if len(trainer_data) > 32 else 0
                        party_pointer = struct.unpack('<I', trainer_data[36:40])[0] if len(trainer_data) >= 40 else 0
                        
                        print(f"  Size {test_size}: Class={trainer_class}, PartySize={party_size}, Pointer=0x{party_pointer:08X}")
                        
                        # Verificar se os valores fazem sentido
                        if (1 <= trainer_class <= 100 and 
                            1 <= party_size <= 6 and 
                            0x08000000 <= party_pointer <= 0x09000000):
                            print(f"    ✅ Estrutura válida com tamanho {test_size}")
                        else:
                            print(f"    ❌ Estrutura inválida com tamanho {test_size}")
                    else:
                        print(f"  Size {test_size}: Muito pequeno para análise completa")
                print()
    
    except Exception as e:
        print(f"  ❌ Erro verificando estrutura: {e}")
    
    # Verificar se há problema na interpretação dos dados do Pokemon
    print("🔍 VERIFICAÇÃO DA ESTRUTURA DE POKEMON:")
    print("-" * 40)
    
    try:
        with open('BPRE0.gba', 'rb') as rom:
            # Usar offset conhecido do Koga
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
            
            if party_pointer >= 0x08000000:
                party_file_offset = party_pointer - 0x08000000
            else:
                party_file_offset = party_pointer
            
            print(f"  Koga party offset: 0x{party_file_offset:08X}")
            print(f"  Koga party size: {party_size}")
            
            # Testar diferentes estruturas de Pokemon
            rom.seek(party_file_offset)
            
            for pokemon_size in [6, 8, 10, 12, 16]:
                print(f"\n  Testando estrutura de Pokemon com {pokemon_size} bytes:")
                
                rom.seek(party_file_offset)
                for slot in range(min(party_size, 2)):  # Testar apenas 2 slots
                    pokemon_data = rom.read(pokemon_size)
                    
                    if len(pokemon_data) >= pokemon_size:
                        print(f"    Slot {slot + 1}: {pokemon_data.hex()}")
                        
                        # Tentar interpretar como diferentes estruturas
                        if pokemon_size >= 8:
                            # Estrutura padrão: IV, ?, Level, ?, Species(2), Item(2)
                            iv = pokemon_data[0]
                            level = pokemon_data[2]
                            species = struct.unpack('<H', pokemon_data[4:6])[0]
                            
                            print(f"      Standard: IV={iv}, Level={level}, Species=#{species}")
                            
                            # Verificar se os valores fazem sentido
                            if 1 <= level <= 100 and 1 <= species <= 1440:
                                print(f"      ✅ Valores válidos")
                            else:
                                print(f"      ❌ Valores inválidos")
                        
                        # Tentar estrutura alternativa
                        if pokemon_size >= 6:
                            # Estrutura alternativa: Species(2), Level, ?, ?, ?
                            alt_species = struct.unpack('<H', pokemon_data[0:2])[0]
                            alt_level = pokemon_data[2]
                            
                            print(f"      Alternative: Species=#{alt_species}, Level={alt_level}")
                            
                            if 1 <= alt_level <= 100 and 1 <= alt_species <= 1440:
                                print(f"      ✅ Valores alternativos válidos")
                            else:
                                print(f"      ❌ Valores alternativos inválidos")
                    print()
    
    except Exception as e:
        print(f"  ❌ Erro verificando Pokemon: {e}")
    
    print("\n🎯 CONCLUSÕES:")
    print("-" * 40)
    print("1. ✅ Nossos offsets principais estão corretos (TrainerData = 0x23EAC8)")
    print("2. 🔍 Verificar se a estrutura de dados está sendo interpretada corretamente")
    print("3. 🔍 Verificar se há diferenças na estrutura de Pokemon")
    print("4. 🔍 Verificar se há problema na conversão de ponteiros")
    
    return True

if __name__ == "__main__":
    compare_official_offsets()
