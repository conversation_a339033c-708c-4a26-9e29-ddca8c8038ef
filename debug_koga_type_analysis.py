#!/usr/bin/env python3
"""
Debug: <PERSON><PERSON><PERSON><PERSON> detalhada dos tipos da party original do Koga
"""

import sys
sys.path.append('scripts')

def analyze_koga_types():
    """Analisa detalhadamente os tipos da party original do Koga"""
    
    print("🔍 ANÁLISE DETALHADA DOS TIPOS - PARTY ORIGINAL DO KOGA")
    print("=" * 60)
    
    try:
        from insert import LoadProjectPokemonDatabase, ConvertTypeNumberToName, AnalyzeOriginalPartyTypesFromCachedData
        
        # Party original do Koga (dados reais da ROM)
        koga_original_party = [
            {"species": 109, "level": 37},  # KOFFING
            {"species": 92, "level": 108},  # GASTLY  
            {"species": 89, "level": 39},   # MUK
            {"species": 92, "level": 151}   # GASTLY
        ]
        
        print("📊 PARTY ORIGINAL DO KOGA:")
        print("-" * 30)
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        if not project_pokemon_data:
            print("❌ Falha ao carregar banco de dados do projeto")
            return
        
        # Analisar cada Pokemon individualmente
        from collections import Counter
        type_counts = Counter()
        party_types = set()
        
        for i, pokemon in enumerate(koga_original_party):
            species = pokemon["species"]
            level = pokemon["level"]
            
            if species in project_pokemon_data:
                pokemon_data = project_pokemon_data[species]
                name = pokemon_data.get('name', f'Unknown #{species}')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                type1_name = ConvertTypeNumberToName(type1) if type1 is not None else "None"
                type2_name = ConvertTypeNumberToName(type2) if type2 is not None and type2 != type1 else "None"
                
                print(f"  Slot {i+1}: #{species} {name}")
                print(f"    Type 1: {type1_name} ({type1})")
                print(f"    Type 2: {type2_name} ({type2})")
                print(f"    Level: {level}")
                
                # Contar tipos
                if type1 is not None:
                    type_counts[type1] += 1
                    party_types.add(type1)
                    print(f"    ✅ Adicionado tipo {type1_name} à contagem")
                
                if type2 is not None and type2 != type1:
                    type_counts[type2] += 1
                    party_types.add(type2)
                    print(f"    ✅ Adicionado tipo {type2_name} à contagem")
                
                print()
            else:
                print(f"  Slot {i+1}: #{species} - NÃO ENCONTRADO NO PROJETO")
                print()
        
        # Mostrar contagem final
        print("📈 CONTAGEM FINAL DE TIPOS:")
        print("-" * 30)
        
        for type_id, count in type_counts.most_common():
            type_name = ConvertTypeNumberToName(type_id)
            print(f"  {type_name} ({type_id}): {count} ocorrências")
        
        # Determinar tipos predominantes
        most_common = type_counts.most_common(2)
        primary_type = most_common[0][0] if most_common else None
        secondary_type = most_common[1][0] if len(most_common) > 1 else None
        
        primary_name = ConvertTypeNumberToName(primary_type) if primary_type else "None"
        secondary_name = ConvertTypeNumberToName(secondary_type) if secondary_type else "None"
        
        print(f"\n🎯 TIPOS PREDOMINANTES:")
        print(f"  Primary: {primary_name} ({primary_type}) - {type_counts[primary_type]} ocorrências")
        print(f"  Secondary: {secondary_name} ({secondary_type}) - {type_counts[secondary_type] if secondary_type else 0} ocorrências")
        
        # Testar a função oficial
        print(f"\n🧪 TESTE DA FUNÇÃO OFICIAL:")
        print("-" * 30)
        
        party_analysis = AnalyzeOriginalPartyTypesFromCachedData(koga_original_party)
        
        official_primary = party_analysis.get('primary_type')
        official_secondary = party_analysis.get('secondary_type')
        official_party_types = party_analysis.get('party_types', set())
        official_type_counts = party_analysis.get('type_counts', Counter())
        
        official_primary_name = ConvertTypeNumberToName(official_primary) if official_primary else "None"
        official_secondary_name = ConvertTypeNumberToName(official_secondary) if official_secondary else "None"
        
        print(f"  Função oficial - Primary: {official_primary_name} ({official_primary})")
        print(f"  Função oficial - Secondary: {official_secondary_name} ({official_secondary})")
        print(f"  Função oficial - Party types: {[ConvertTypeNumberToName(t) for t in official_party_types]}")
        print(f"  Função oficial - Type counts: {dict(official_type_counts)}")
        
        # Verificar se há discrepância
        print(f"\n🔍 VERIFICAÇÃO DE CONSISTÊNCIA:")
        print("-" * 30)
        
        if primary_type == official_primary:
            print(f"  ✅ Primary type consistente: {primary_name}")
        else:
            print(f"  ❌ Primary type INCONSISTENTE!")
            print(f"     Manual: {primary_name} ({primary_type})")
            print(f"     Função: {official_primary_name} ({official_primary})")
        
        if secondary_type == official_secondary:
            print(f"  ✅ Secondary type consistente: {secondary_name}")
        else:
            print(f"  ❌ Secondary type INCONSISTENTE!")
            print(f"     Manual: {secondary_name} ({secondary_type})")
            print(f"     Função: {official_secondary_name} ({official_secondary})")
        
        # Explicar por que Ghost pode ter sido escolhido
        print(f"\n💡 ANÁLISE DO PROBLEMA:")
        print("-" * 30)
        
        poison_count = type_counts.get(3, 0)  # POISON = 3
        ghost_count = type_counts.get(7, 0)   # GHOST = 7
        
        print(f"  Poison ocorrências: {poison_count}")
        print(f"  Ghost ocorrências: {ghost_count}")
        
        if poison_count > ghost_count:
            print(f"  🎯 POISON deveria ser predominante ({poison_count} > {ghost_count})")
            print(f"  ❌ Se Ghost foi escolhido, há um bug na lógica de seleção")
        elif ghost_count > poison_count:
            print(f"  🤔 Ghost é realmente predominante ({ghost_count} > {poison_count})")
        else:
            print(f"  ⚖️  Empate entre Poison e Ghost ({poison_count} = {ghost_count})")
            print(f"  📝 Neste caso, a ordem de processamento determina o resultado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_koga_types()
