#!/usr/bin/env python3
"""
Verificação da integridade da ROM Fire Red
Confirmar se BPRE0.gba é uma ROM Fire Red (U) 1.0 limpa
"""

import sys
import struct
import hashlib

def verify_rom_integrity():
    """Verifica se a ROM é Fire Red (U) 1.0 limpa"""
    
    print("🔍 VERIFICAÇÃO: INTEGRIDADE DA ROM FIRE RED")
    print("=" * 60)
    
    try:
        with open('BPRE0.gba', 'rb') as rom:
            # Read entire ROM for checksum
            rom_data = rom.read()
            rom_size = len(rom_data)
            
            print(f"📊 INFORMAÇÕES BÁSICAS:")
            print(f"   Tamanho da ROM: {rom_size:,} bytes ({rom_size / (1024*1024):.1f} MB)")
            
            # Check ROM header
            rom.seek(0xA0)
            game_code = rom_data[0xA0:0xA4].decode('ascii', errors='ignore')
            
            rom.seek(0xAC)
            game_title = rom_data[0xAC:0xB8].decode('ascii', errors='ignore').strip('\x00')
            
            rom.seek(0xBC)
            version = rom_data[0xBC]
            
            print(f"   Game Code: {game_code}")
            print(f"   Game Title: {game_title}")
            print(f"   Version: {version}")
            
            # Expected values for Fire Red (U) 1.0
            expected_game_code = "BPRE"
            expected_version = 0
            
            if game_code == expected_game_code and version == expected_version:
                print("   ✅ ROM identificada como Fire Red (U) 1.0")
            else:
                print("   ❌ ROM NÃO é Fire Red (U) 1.0 padrão")
                print(f"      Esperado: Game Code = {expected_game_code}, Version = {expected_version}")
                print(f"      Encontrado: Game Code = {game_code}, Version = {version}")
            
            # Calculate checksums
            print(f"\n🔐 CHECKSUMS:")
            
            # MD5
            md5_hash = hashlib.md5(rom_data).hexdigest().upper()
            print(f"   MD5: {md5_hash}")
            
            # SHA1
            sha1_hash = hashlib.sha1(rom_data).hexdigest().upper()
            print(f"   SHA1: {sha1_hash}")
            
            # CRC32 (from offsets file)
            import zlib
            crc32_hash = format(zlib.crc32(rom_data) & 0xffffffff, '08X')
            print(f"   CRC32: {crc32_hash}")
            
            # Expected CRC32 from .offsets/gen3_offsets.ini
            expected_crc32 = "DD88761C"
            
            if crc32_hash == expected_crc32:
                print("   ✅ CRC32 corresponde ao Fire Red (U) 1.0 oficial")
            else:
                print("   ❌ CRC32 NÃO corresponde ao Fire Red (U) 1.0 oficial")
                print(f"      Esperado: {expected_crc32}")
                print(f"      Encontrado: {crc32_hash}")
            
            # Check specific known data points
            print(f"\n🎯 VERIFICAÇÃO DE DADOS ESPECÍFICOS:")
            
            # Check trainer table offset
            trainer_table_offset = 0x23EAC8
            rom.seek(trainer_table_offset)
            trainer_data = rom_data[trainer_table_offset:trainer_table_offset + 40]
            
            if len(trainer_data) >= 40:
                trainer_class = trainer_data[1]
                party_size = trainer_data[32]
                party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
                
                print(f"   Trainer Table (offset 0x{trainer_table_offset:08X}):")
                print(f"     First trainer class: {trainer_class}")
                print(f"     First trainer party size: {party_size}")
                print(f"     First trainer party pointer: 0x{party_pointer:08X}")
                
                # These should be reasonable values for a clean ROM
                if 1 <= trainer_class <= 100 and 1 <= party_size <= 6:
                    print("     ✅ Dados do trainer table parecem válidos")
                else:
                    print("     ❌ Dados do trainer table parecem inválidos")
            
            # Check Pokemon stats offset
            pokemon_stats_offset = 0x254784
            rom.seek(pokemon_stats_offset)
            pokemon_stats_data = rom_data[pokemon_stats_offset:pokemon_stats_offset + 28]
            
            if len(pokemon_stats_data) >= 28:
                # First Pokemon should be Bulbasaur
                hp = pokemon_stats_data[0]
                attack = pokemon_stats_data[1]
                defense = pokemon_stats_data[2]
                speed = pokemon_stats_data[3]
                sp_attack = pokemon_stats_data[4]
                sp_defense = pokemon_stats_data[5]
                type1 = pokemon_stats_data[6]
                type2 = pokemon_stats_data[7]
                
                print(f"   Pokemon Stats (offset 0x{pokemon_stats_offset:08X}):")
                print(f"     First Pokemon (Bulbasaur): HP={hp}, ATK={attack}, DEF={defense}")
                print(f"     Types: {type1}/{type2}")
                
                # Bulbasaur's expected stats: HP=45, ATK=49, DEF=49, Types=12/3 (Grass/Poison)
                if hp == 45 and attack == 49 and defense == 49 and type1 == 12 and type2 == 3:
                    print("     ✅ Dados do Bulbasaur estão corretos")
                else:
                    print("     ❌ Dados do Bulbasaur estão incorretos")
                    print("     Esperado: HP=45, ATK=49, DEF=49, Type1=12, Type2=3")
            
            # Check for common ROM hacks signatures
            print(f"\n🔍 VERIFICAÇÃO DE MODIFICAÇÕES:")
            
            # Check for common hack signatures
            hack_signatures = [
                (b"COMPLETE FIRE RED UPGRADE", "Complete Fire Red Upgrade"),
                (b"CFRU", "CFRU"),
                (b"EXPANSION", "Pokemon Expansion"),
                (b"RANDOMIZER", "Randomizer"),
                (b"HACK", "Generic Hack"),
            ]
            
            found_signatures = []
            for signature, name in hack_signatures:
                if signature in rom_data:
                    found_signatures.append(name)
            
            if found_signatures:
                print("   ⚠️  Possíveis modificações detectadas:")
                for sig in found_signatures:
                    print(f"     - {sig}")
            else:
                print("   ✅ Nenhuma assinatura de hack detectada")
            
            # Final assessment
            print(f"\n🎯 AVALIAÇÃO FINAL:")
            
            is_correct_rom = (
                game_code == expected_game_code and
                version == expected_version and
                crc32_hash == expected_crc32
            )
            
            if is_correct_rom:
                print("   ✅ ROM é Fire Red (U) 1.0 limpa e correta")
                print("   ✅ Pode ser usada com segurança para o projeto")
            else:
                print("   ❌ ROM NÃO é Fire Red (U) 1.0 limpa")
                print("   ❌ Isso pode explicar os problemas observados")
                print()
                print("   🔧 SOLUÇÕES:")
                print("   1. Obter uma ROM Fire Red (U) 1.0 limpa")
                print("   2. Verificar se o arquivo não está corrompido")
                print("   3. Renomear a ROM correta para 'BPRE0.gba'")
                print("   4. Colocar no diretório raiz do projeto")
            
            return is_correct_rom
            
    except FileNotFoundError:
        print("❌ Arquivo BPRE0.gba não encontrado")
        print("   Certifique-se de que a ROM está no diretório raiz")
        print("   e está nomeada corretamente como 'BPRE0.gba'")
        return False
    
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_rom_integrity()
