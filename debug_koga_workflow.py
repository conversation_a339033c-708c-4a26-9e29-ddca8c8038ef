#!/usr/bin/env python3
"""
Debug detalhado do workflow de randomização do Koga
Identificar por que Ponyta e Growlithe aparecem em vez de Budew e Naganadel
"""

import sys
import struct
sys.path.append('scripts')

def debug_koga_workflow():
    """Debug detalhado do workflow do Koga"""
    
    print("🔍 DEBUG: WORKFLOW DE RANDOMIZAÇÃO DO KOGA")
    print("=" * 60)
    
    try:
        from insert import (LoadProjectPokemonDatabase, ConvertTypeNumberToName, 
                           AnalyzeOriginalPartyTypesFromCachedData, ShouldRandomizeOnlyAdditionalPokemon)
        
        # STEP 1: Verificar configuração
        print("⚙️ STEP 1: Verificando configuração")
        print("-" * 40)
        
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()
        print(f"  RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        
        # STEP 2: Simular party original do Koga
        print("\n📊 STEP 2: Party original do Koga")
        print("-" * 40)
        
        koga_original_party = [
            {"species": 109, "level": 37},  # KOFFING
            {"species": 92, "level": 108},  # GASTLY  
            {"species": 89, "level": 39},   # MUK
            {"species": 92, "level": 151}   # GASTLY
        ]
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        for i, pokemon in enumerate(koga_original_party):
            species = pokemon["species"]
            level = pokemon["level"]
            
            if species in project_pokemon_data:
                pokemon_data = project_pokemon_data[species]
                name = pokemon_data.get('name', f'Unknown #{species}')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                type1_name = ConvertTypeNumberToName(type1) if type1 is not None else "None"
                type2_name = ConvertTypeNumberToName(type2) if type2 is not None and type2 != type1 else "None"
                
                print(f"  Slot {i+1}: #{species} {name} ({type1_name}/{type2_name})")
        
        # STEP 3: Análise de tipos
        print("\n🎯 STEP 3: Análise de tipos")
        print("-" * 40)
        
        party_analysis = AnalyzeOriginalPartyTypesFromCachedData(koga_original_party)
        
        primary_type = party_analysis.get('primary_type')
        secondary_type = party_analysis.get('secondary_type')
        party_types = party_analysis.get('party_types', set())
        type_counts = party_analysis.get('type_counts', {})
        
        primary_name = ConvertTypeNumberToName(primary_type) if primary_type else "None"
        secondary_name = ConvertTypeNumberToName(secondary_type) if secondary_type else "None"
        
        print(f"  Primary type: {primary_name} ({primary_type}) - {type_counts.get(primary_type, 0)} ocorrências")
        print(f"  Secondary type: {secondary_name} ({secondary_type}) - {type_counts.get(secondary_type, 0)} ocorrências")
        print(f"  Party types: {[ConvertTypeNumberToName(t) for t in party_types]}")
        
        # STEP 4: Verificar Pokemon específicos
        print("\n🔍 STEP 4: Verificando Pokemon específicos")
        print("-" * 40)
        
        pokemon_to_check = {
            "PONYTA": 77,
            "GROWLITHE": 58,
            "BUDEW": 459,
            "NAGANADEL": 1075
        }
        
        poison_type = 3  # POISON = 3
        
        for name, species_id in pokemon_to_check.items():
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                type1_name = ConvertTypeNumberToName(type1) if type1 else "None"
                type2_name = ConvertTypeNumberToName(type2) if type2 else "None"
                bst = pokemon_data.get('bst', 0)
                
                has_poison = (type1 == poison_type or type2 == poison_type)
                match_status = "✅ MATCHES" if has_poison else "❌ NO MATCH"
                
                print(f"  {name} (#{species_id}):")
                print(f"    Types: {type1_name}/{type2_name}")
                print(f"    BST: {bst}")
                print(f"    Poison match: {match_status}")
                print()
        
        # STEP 5: Verificar se há problema na seleção
        print("🎲 STEP 5: Simulando seleção de Pokemon")
        print("-" * 40)
        
        # Simular a lógica de seleção
        print("  Simulando SelectAdditionalPokemonWithoutRandomization...")
        
        # Check if Ponyta and Growlithe would be selected by the current logic
        candidates_primary = []
        candidates_secondary = []
        
        for species_id in range(1, 1441):
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                # Check primary type match (Poison)
                if type1 == primary_type or type2 == primary_type:
                    candidates_primary.append(species_id)
                
                # Check secondary type match (Ghost)
                elif secondary_type and (type1 == secondary_type or type2 == secondary_type):
                    candidates_secondary.append(species_id)
        
        print(f"  Candidatos com tipo primário (Poison): {len(candidates_primary)}")
        print(f"  Candidatos com tipo secundário (Ghost): {len(candidates_secondary)}")
        
        # Check if Ponyta and Growlithe are in the candidates
        ponyta_in_primary = 77 in candidates_primary
        growlithe_in_primary = 58 in candidates_primary
        ponyta_in_secondary = 77 in candidates_secondary
        growlithe_in_secondary = 58 in candidates_secondary
        
        print(f"  PONYTA em candidatos primários: {ponyta_in_primary}")
        print(f"  PONYTA em candidatos secundários: {ponyta_in_secondary}")
        print(f"  GROWLITHE em candidatos primários: {growlithe_in_primary}")
        print(f"  GROWLITHE em candidatos secundários: {growlithe_in_secondary}")
        
        # STEP 6: Verificar se há múltiplas funções de randomização
        print("\n🔄 STEP 6: Verificando múltiplas funções de randomização")
        print("-" * 40)
        
        print("  POSSÍVEIS CAUSAS:")
        print("  1. 🔄 RandomizeOnlyAdditionalPokemonWithCache está sendo chamado APÓS a expansão")
        print("  2. 📝 Múltiplas escritas nos mesmos slots da ROM")
        print("  3. ⚙️  Configuração RANDOMIZE_ONLY_ADDITIONAL não está sendo respeitada")
        print("  4. 🎲 Função de randomização está sobrescrevendo seleções corretas")
        
        # STEP 7: Verificar se Ponyta/Growlithe são placeholders
        print("\n🎯 STEP 7: Verificando se são placeholders")
        print("-" * 40)
        
        # Check if Ponyta and Growlithe might be used as type representatives
        fire_type = 10  # FIRE = 10
        
        ponyta_data = project_pokemon_data.get(77)
        growlithe_data = project_pokemon_data.get(58)
        
        if ponyta_data:
            p_type1 = ponyta_data.get('type1')
            p_type2 = ponyta_data.get('type2')
            print(f"  PONYTA: Type1={p_type1}, Type2={p_type2}")
            if p_type1 == fire_type or p_type2 == fire_type:
                print(f"    ⚠️  PONYTA é Fire-type, pode ser placeholder incorreto")
        
        if growlithe_data:
            g_type1 = growlithe_data.get('type1')
            g_type2 = growlithe_data.get('type2')
            print(f"  GROWLITHE: Type1={g_type1}, Type2={g_type2}")
            if g_type1 == fire_type or g_type2 == fire_type:
                print(f"    ⚠️  GROWLITHE é Fire-type, pode ser placeholder incorreto")
        
        # STEP 8: Conclusões
        print("\n🎯 STEP 8: Conclusões")
        print("-" * 40)
        
        print("  HIPÓTESE PRINCIPAL:")
        print("  🔄 O sistema está fazendo DUAS passadas:")
        print("     1. Expansão: Seleciona Budew e Naganadel (correto)")
        print("     2. Randomização: Sobrescreve com Ponyta e Growlithe (incorreto)")
        print()
        print("  EVIDÊNCIAS:")
        print("  ✅ Logs mostram seleção correta (Budew/Naganadel)")
        print("  ❌ ROM final contém Pokemon diferentes (Ponyta/Growlithe)")
        print("  ⚠️  RANDOMIZE_ONLY_ADDITIONAL deveria prevenir isso")
        print()
        print("  SOLUÇÃO NECESSÁRIA:")
        print("  🛡️  Garantir que RandomizeOnlyAdditionalPokemonWithCache")
        print("     NÃO modifique species quando RANDOMIZE_ONLY_ADDITIONAL = TRUE")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_koga_workflow()
