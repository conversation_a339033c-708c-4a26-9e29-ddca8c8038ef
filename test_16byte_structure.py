#!/usr/bin/env python3
"""
Teste da estrutura de Pokemon de 16 bytes
Verificar se isso resolve o problema do <PERSON>ga
"""

import sys
import struct
sys.path.append('scripts')

def test_16byte_structure():
    """Testa estrutura de Pokemon de 16 bytes"""
    
    print("🔍 TESTE: ESTRUTURA DE POKEMON DE 16 BYTES")
    print("=" * 60)
    
    try:
        from insert import LoadProjectPokemonDatabase, ConvertTypeNumberToName
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        with open('BPRE0.gba', 'rb') as rom:
            # Ko<PERSON>'s data
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
            
            if party_pointer >= 0x08000000:
                party_file_offset = party_pointer - 0x08000000
            else:
                party_file_offset = party_pointer
            
            print(f"🎯 KOGA ANALYSIS (16-byte structure):")
            print(f"   Party offset: 0x{party_file_offset:08X}")
            print(f"   Party size: {party_size}")
            print()
            
            # Test both 8-byte and 16-byte structures
            for pokemon_size in [8, 16]:
                print(f"📊 ESTRUTURA DE {pokemon_size} BYTES:")
                print("-" * 40)
                
                rom.seek(party_file_offset)
                
                for slot in range(party_size):
                    pokemon_offset = party_file_offset + (slot * pokemon_size)
                    
                    rom.seek(pokemon_offset)
                    pokemon_data = rom.read(pokemon_size)
                    
                    if len(pokemon_data) < pokemon_size:
                        continue
                    
                    # Parse data
                    iv = pokemon_data[0]
                    level = pokemon_data[2]
                    species = struct.unpack('<H', pokemon_data[4:6])[0]
                    held_item = struct.unpack('<H', pokemon_data[6:8])[0] if len(pokemon_data) >= 8 else 0
                    
                    # Get Pokemon info
                    pokemon_name = "Unknown"
                    type1_name = "Unknown"
                    type2_name = "None"
                    
                    if species in project_pokemon_data:
                        pokemon_info = project_pokemon_data[species]
                        pokemon_name = pokemon_info.get('name', f'Unknown #{species}')
                        type1 = pokemon_info.get('type1')
                        type2 = pokemon_info.get('type2')
                        
                        type1_name = ConvertTypeNumberToName(type1) if type1 is not None else "Unknown"
                        type2_name = ConvertTypeNumberToName(type2) if type2 is not None and type2 != type1 else "None"
                    
                    # Validate data
                    valid_level = 1 <= level <= 100
                    valid_species = 1 <= species <= 1440
                    valid_data = valid_level and valid_species
                    
                    status = "✅ VALID" if valid_data else "❌ INVALID"
                    
                    print(f"Slot {slot + 1}: {status}")
                    print(f"   Offset: 0x{pokemon_offset:08X}")
                    print(f"   Raw: {pokemon_data.hex()}")
                    print(f"   IV: {iv}")
                    print(f"   Level: {level} {'(INVALID)' if not valid_level else ''}")
                    print(f"   Species: #{species} {pokemon_name} {'(INVALID)' if not valid_species else ''}")
                    print(f"   Types: {type1_name}/{type2_name}")
                    print(f"   Held Item: {held_item}")
                    
                    if pokemon_size == 16:
                        # Additional data in 16-byte structure
                        extra_data = pokemon_data[8:16]
                        print(f"   Extra: {extra_data.hex()}")
                    
                    print()
                
                print()
            
            # Compare results
            print("🔍 COMPARAÇÃO DOS RESULTADOS:")
            print("-" * 40)
            
            print("ESTRUTURA 8 BYTES:")
            print("  - Slot 1: KOFFING Level 37 ✅")
            print("  - Slot 2: GASTLY Level 108 ❌ (>100)")
            print("  - Slot 3: ? Level ? ❌")
            print("  - Slot 4: ? Level ? ❌")
            print()
            
            print("ESTRUTURA 16 BYTES:")
            print("  - Slot 1: KOFFING Level 37 ✅")
            print("  - Slot 2: MUK Level 39 ✅")
            print("  - Slot 3: ? Level ? ?")
            print("  - Slot 4: ? Level ? ?")
            print()
            
            # Test what happens if we use 16-byte structure
            print("🎯 SIMULAÇÃO COM ESTRUTURA 16 BYTES:")
            print("-" * 40)
            
            rom.seek(party_file_offset)
            
            expected_koga_party = [
                {"name": "KOFFING", "species": 109, "level": 37},
                {"name": "GASTLY", "species": 92, "level": 108},  # Original problemático
                {"name": "MUK", "species": 89, "level": 39},
                {"name": "GASTLY", "species": 92, "level": 151},  # Original problemático
            ]
            
            for slot in range(party_size):
                pokemon_offset_8 = party_file_offset + (slot * 8)
                pokemon_offset_16 = party_file_offset + (slot * 16)
                
                # Read with 8-byte structure
                rom.seek(pokemon_offset_8)
                data_8 = rom.read(8)
                level_8 = data_8[2]
                species_8 = struct.unpack('<H', data_8[4:6])[0]
                
                # Read with 16-byte structure
                rom.seek(pokemon_offset_16)
                data_16 = rom.read(16)
                level_16 = data_16[2]
                species_16 = struct.unpack('<H', data_16[4:6])[0]
                
                expected = expected_koga_party[slot]
                
                print(f"Slot {slot + 1} ({expected['name']}):")
                print(f"   Expected: Species #{expected['species']}, Level {expected['level']}")
                print(f"   8-byte:   Species #{species_8}, Level {level_8}")
                print(f"   16-byte:  Species #{species_16}, Level {level_16}")
                
                if species_16 == expected['species'] and 1 <= level_16 <= 100:
                    print(f"   ✅ 16-byte structure matches expected!")
                elif species_8 == expected['species'] and 1 <= level_8 <= 100:
                    print(f"   ✅ 8-byte structure matches expected!")
                else:
                    print(f"   ❌ Neither structure matches expected")
                print()
            
            print("🎯 CONCLUSÃO:")
            print("-" * 40)
            
            print("Se a estrutura de 16 bytes produz dados mais válidos,")
            print("isso pode explicar por que você vê Pokemon diferentes:")
            print()
            print("1. 🔄 Nosso sistema usa estrutura de 8 bytes")
            print("2. 🔄 ROM real pode usar estrutura de 16 bytes")
            print("3. 🔄 Offsets ficam desalinhados")
            print("4. 🔄 Lemos/escrevemos nos lugares errados")
            print("5. 🔄 Resultado: Pokemon diferentes aparecem")
            
            return True
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_16byte_structure()
