#!/usr/bin/env python3
"""
Análise crítica: Gastly com levels problemáticos (108, 151)
Isso pode indicar corrupção de dados ou interpretação incorreta de bytes
"""

import sys
import struct
sys.path.append('scripts')

def analyze_gastly_corruption():
    """Analisa a corrupção dos dados do Gastly"""
    
    print("🚨 ANÁLISE CRÍTICA: CORRUPÇÃO DOS GASTLY")
    print("=" * 60)
    
    try:
        # Open the modified ROM
        rom_file = "test.gba"
        with open(rom_file, 'rb') as rom:
            print(f"✅ ROM modificada aberta: {rom_file}")
            
            # <PERSON><PERSON>'s party data
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            # Read Ko<PERSON>'s trainer data
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
            
            if party_pointer >= 0x08000000:
                party_file_offset = party_pointer - 0x08000000
            else:
                party_file_offset = party_pointer
            
            print(f"🎯 KOGA PARTY OFFSET: 0x{party_file_offset:08X}")
            print(f"🎯 PARTY SIZE: {party_size}")
            print()
            
            # Analyze each Pokemon slot in detail
            rom.seek(party_file_offset)
            
            for slot in range(party_size):
                pokemon_offset = party_file_offset + (slot * 8)
                
                print(f"🔍 SLOT {slot + 1} ANALYSIS:")
                print("-" * 30)
                
                # Read raw bytes
                rom.seek(pokemon_offset)
                pokemon_data = rom.read(8)
                
                if len(pokemon_data) < 8:
                    print("   ❌ Dados insuficientes")
                    continue
                
                # Parse individual bytes
                byte0 = pokemon_data[0]  # IV
                byte1 = pokemon_data[1]  # Unknown/padding
                byte2 = pokemon_data[2]  # Level
                byte3 = pokemon_data[3]  # Unknown/padding
                species_bytes = pokemon_data[4:6]  # Species (2 bytes)
                item_bytes = pokemon_data[6:8]    # Held item (2 bytes)
                
                species = struct.unpack('<H', species_bytes)[0]
                held_item = struct.unpack('<H', item_bytes)[0]
                
                print(f"   Offset: 0x{pokemon_offset:08X}")
                print(f"   Raw bytes: {pokemon_data.hex()}")
                print(f"   Byte breakdown:")
                print(f"     [0] IV: {byte0} (0x{byte0:02X})")
                print(f"     [1] Pad: {byte1} (0x{byte1:02X})")
                print(f"     [2] Level: {byte2} (0x{byte2:02X})")
                print(f"     [3] Pad: {byte3} (0x{byte3:02X})")
                print(f"     [4-5] Species: {species} (0x{species:04X})")
                print(f"     [6-7] Item: {held_item} (0x{held_item:04X})")
                
                # Check for anomalies
                if byte2 > 100:
                    print(f"   🚨 LEVEL ANÔMALO: {byte2} (>100)")
                    
                    # Check if this might be a different interpretation
                    # Maybe the bytes are in wrong order or corrupted
                    alt_level_1 = byte0  # Maybe IV is actually level?
                    alt_level_2 = byte1  # Maybe padding is actually level?
                    alt_level_3 = byte3  # Maybe other padding is level?
                    
                    print(f"   🔍 Interpretações alternativas:")
                    print(f"     Se byte[0] fosse level: {alt_level_1}")
                    print(f"     Se byte[1] fosse level: {alt_level_2}")
                    print(f"     Se byte[3] fosse level: {alt_level_3}")
                    
                    # Check if species might be corrupted too
                    if species == 92:  # Gastly
                        print(f"   👻 Species: GASTLY (correto)")
                    else:
                        print(f"   ❌ Species: #{species} (não é Gastly!)")
                        
                        # Check if species bytes might be swapped
                        species_swapped = struct.unpack('>H', species_bytes)[0]  # Big-endian
                        print(f"   🔄 Species (big-endian): #{species_swapped}")
                
                # Check if this slot should have been modified
                slot_type = "ORIGINAL" if slot < 4 else "ADDITIONAL"
                if slot_type == "ORIGINAL" and byte2 > 100:
                    print(f"   🚨 SLOT ORIGINAL COM LEVEL ANÔMALO!")
                    print(f"   🚨 Isso indica possível corrupção durante randomização")
                
                print()
            
            # CRITICAL: Check if the problematic Gastly slots were supposed to be different Pokemon
            print("🔍 ANÁLISE CRÍTICA: SLOTS PROBLEMÁTICOS")
            print("=" * 50)
            
            # Check what the original ROM had for Koga
            print("📖 Verificando ROM original...")
            
            try:
                with open('BPRE0.gba', 'rb') as original_rom:
                    # Read original Koga data
                    original_rom.seek(koga_offset)
                    original_trainer_data = original_rom.read(40)
                    
                    original_party_size = original_trainer_data[32]
                    original_party_pointer = struct.unpack('<I', original_trainer_data[36:40])[0]
                    
                    if original_party_pointer >= 0x08000000:
                        original_party_file_offset = original_party_pointer - 0x08000000
                    else:
                        original_party_file_offset = original_party_pointer
                    
                    print(f"Original party size: {original_party_size}")
                    print(f"Original party offset: 0x{original_party_file_offset:08X}")
                    
                    # Read original party
                    original_rom.seek(original_party_file_offset)
                    
                    print("\n📊 COMPARAÇÃO ORIGINAL vs MODIFICADO:")
                    print("-" * 40)
                    
                    for slot in range(min(original_party_size, party_size)):
                        # Original data
                        original_pokemon_data = original_rom.read(8)
                        if len(original_pokemon_data) < 8:
                            continue
                        
                        orig_level = original_pokemon_data[2]
                        orig_species = struct.unpack('<H', original_pokemon_data[4:6])[0]
                        
                        # Modified data (re-read)
                        rom.seek(party_file_offset + (slot * 8))
                        mod_pokemon_data = rom.read(8)
                        mod_level = mod_pokemon_data[2]
                        mod_species = struct.unpack('<H', mod_pokemon_data[4:6])[0]
                        
                        print(f"Slot {slot + 1}:")
                        print(f"   Original: Species #{orig_species}, Level {orig_level}")
                        print(f"   Modified: Species #{mod_species}, Level {mod_level}")
                        
                        if orig_level != mod_level:
                            print(f"   🚨 LEVEL CHANGED: {orig_level} → {mod_level}")
                        if orig_species != mod_species:
                            print(f"   🚨 SPECIES CHANGED: #{orig_species} → #{mod_species}")
                        
                        # Check if the problematic levels match something else
                        if mod_level > 100:
                            print(f"   🔍 Level {mod_level} pode ser:")
                            if mod_level == 108:
                                print(f"     - 0x6C em hex = 108 decimal")
                                print(f"     - Pode ser species #{108} interpretado como level")
                            elif mod_level == 151:
                                print(f"     - 0x97 em hex = 151 decimal")
                                print(f"     - Pode ser species #{151} (Mew) interpretado como level")
                        print()
                        
            except FileNotFoundError:
                print("❌ ROM original (BPRE0.gba) não encontrada")
            
            # HYPOTHESIS: Check if the expansion process corrupted original slots
            print("🔍 HIPÓTESES SOBRE A CORRUPÇÃO:")
            print("-" * 40)
            print("1. 🔄 Expansion process escreveu nos slots originais por engano")
            print("2. 🔄 Offset calculation error durante a escrita")
            print("3. 🔄 Boundary protection falhou")
            print("4. 🔄 Species IDs foram interpretados como levels")
            print("5. 🔄 Byte order confusion (little-endian vs big-endian)")
            print("6. 🔄 Randomização modificou slots que deveria preservar")
            
            # Check if levels 108 and 151 correspond to any Pokemon IDs
            print(f"\n🔍 VERIFICAÇÃO DE SPECIES IDs:")
            print(f"   Species #108: Pode ser LICKITUNG")
            print(f"   Species #151: Pode ser MEW")
            print(f"   Esses IDs podem ter sido escritos no campo level por engano")
            
            return True
            
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_gastly_corruption()
