#!/usr/bin/env python3
"""
Debug: Por que Koga recebeu Ponyta (#77 = 0x4D) em vez de Pokémon POISON?
"""

import sys
import os

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_koga_ponyta_issue():
    """Investiga por que Koga recebeu Ponyta"""
    
    print("🔍 DEBUG: Por que Koga recebeu Ponyta (#77 = 0x4D)?")
    print("=" * 60)
    
    try:
        from insert import (
            LoadProjectPokemonDatabase,
            ReadAllTrainerDataFromOriginalROM,
            AnalyzeOriginalPartyTypesFromCachedData,
            SelectAdditionalPokemonWithAppropriateTypes
        )
        
        # Carregar dados
        project_pokemon_data = LoadProjectPokemonDatabase()
        original_trainer_cache = ReadAllTrainerDataFromOriginalROM()
        
        if not original_trainer_cache:
            print("❌ Falha ao ler dados cached")
            return
        
        cached_trainers = original_trainer_cache['trainers']
        
        # Koga ID
        koga_id = 418
        
        if koga_id not in cached_trainers:
            print(f"❌ Koga (ID {koga_id}) não encontrado")
            return
        
        koga_data = cached_trainers[koga_id]
        original_party = koga_data['original_party']
        
        print(f"🎯 ANÁLISE DE KOGA:")
        print(f"   Trainer ID: {koga_id}")
        print(f"   Party original: {len(original_party)} Pokémon")
        
        # Mapeamento de tipos
        type_names = {
            0: "NORMAL", 1: "FIGHTING", 2: "FLYING", 3: "POISON", 4: "GROUND",
            5: "ROCK", 6: "BUG", 7: "GHOST", 8: "STEEL", 9: "FIRE",
            10: "WATER", 11: "GRASS", 12: "ELECTRIC", 13: "PSYCHIC", 14: "ICE",
            15: "DRAGON", 16: "DARK", 17: "FAIRY", 18: "ROOSTLESS", 19: "BLANK"
        }
        
        # Analisar party original de Koga
        print(f"\n📋 PARTY ORIGINAL DE KOGA:")
        for i, pokemon in enumerate(original_party):
            species = pokemon.get('species', 'Unknown')
            level = pokemon.get('level', 'Unknown')
            
            if species in project_pokemon_data:
                pokemon_data = project_pokemon_data[species]
                name = pokemon_data.get('name', f'Species_{species}')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                
                type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                print(f"   Slot {i+1}: #{species:3d} {name:15s} Level {level} ({type_str})")
            else:
                print(f"   Slot {i+1}: #{species:3d} {'Unknown':15s} Level {level} (Não encontrado)")
        
        # Analisar tipos da party
        party_type_analysis = AnalyzeOriginalPartyTypesFromCachedData(original_party)
        
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())
        type_counts = party_type_analysis.get('type_counts', {})
        
        print(f"\n🔍 ANÁLISE DE TIPOS DA PARTY:")
        print(f"   Primary type: {type_names.get(primary_type, f'Type_{primary_type}') if primary_type is not None else 'None'}")
        print(f"   Secondary type: {type_names.get(secondary_type, f'Type_{secondary_type}') if secondary_type is not None else 'None'}")
        print(f"   Party types: {[type_names.get(t, f'Type_{t}') for t in party_types]}")
        print(f"   Type counts: {dict(type_counts)}")
        
        # Verificar Ponyta especificamente
        ponyta_species = 77  # 0x4D
        
        print(f"\n🐴 ANÁLISE DE PONYTA (#77 = 0x4D):")
        if ponyta_species in project_pokemon_data:
            ponyta_data = project_pokemon_data[ponyta_species]
            ponyta_name = ponyta_data.get('name', f'Species_{ponyta_species}')
            ponyta_type1 = ponyta_data.get('type1')
            ponyta_type2 = ponyta_data.get('type2')
            
            ponyta_type1_name = type_names.get(ponyta_type1, f'Type_{ponyta_type1}') if ponyta_type1 is not None else 'None'
            ponyta_type2_name = type_names.get(ponyta_type2, f'Type_{ponyta_type2}') if ponyta_type2 is not None and ponyta_type2 != ponyta_type1 else None
            
            ponyta_type_str = f"{ponyta_type1_name}" + (f"/{ponyta_type2_name}" if ponyta_type2_name else "")
            
            print(f"   Nome: {ponyta_name}")
            print(f"   Tipos: {ponyta_type_str}")
            print(f"   Type1: {ponyta_type1} ({ponyta_type1_name})")
            print(f"   Type2: {ponyta_type2} ({ponyta_type2_name if ponyta_type2_name else 'None'})")
            
            # Verificar se Ponyta tem alguma relação com os tipos de Koga
            ponyta_matches_koga = False
            if ponyta_type1 in party_types or (ponyta_type2 is not None and ponyta_type2 in party_types):
                ponyta_matches_koga = True
                print(f"   ✅ COMBINA com tipos de Koga: {ponyta_matches_koga}")
            else:
                print(f"   ❌ NÃO COMBINA com tipos de Koga")
                print(f"   🔍 Tipos de Koga: {[type_names.get(t, f'Type_{t}') for t in party_types]}")
                print(f"   🔍 Tipos de Ponyta: {ponyta_type_str}")
        else:
            print(f"   ❌ Ponyta não encontrado no banco de dados do projeto")
        
        # Simular seleção de Pokémon para Koga
        print(f"\n🧪 SIMULAÇÃO: Seleção de Pokémon para Koga")
        
        try:
            trainer_class = koga_data['trainer_class']
            trainer_category = "BOSS"  # Koga é boss trainer
            max_additional = 2  # Koga pode receber +2 (party size 4→6)
            base_level = 40  # Level aproximado
            
            with open("BPRE0.gba", "rb") as original_rom:
                pokemon_stats_offset = 0x254784
                selected_pokemon = SelectAdditionalPokemonWithAppropriateTypes(
                    original_rom, party_type_analysis, trainer_class, koga_id,
                    trainer_category, max_additional, base_level, pokemon_stats_offset)
            
            print(f"   🎲 Pokémon selecionados: {selected_pokemon}")
            
            # Analisar cada Pokémon selecionado
            for i, species in enumerate(selected_pokemon):
                if species in project_pokemon_data:
                    pokemon_data = project_pokemon_data[species]
                    name = pokemon_data.get('name', f'Species_{species}')
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')
                    
                    type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                    type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                    
                    type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                    
                    # Verificar se combina com Koga
                    matches = []
                    if type1 in party_types:
                        matches.append(type1_name)
                    if type2 is not None and type2 in party_types:
                        matches.append(type2_name)
                    
                    match_str = f" ✅ Combina: {', '.join(matches)}" if matches else " ❌ Não combina"
                    
                    print(f"   Pokémon {i+1}: #{species:3d} {name:15s} ({type_str}){match_str}")
                    
                    # Se for Ponyta, investigar mais
                    if species == ponyta_species:
                        print(f"      🚨 ENCONTRADO PONYTA! Investigando...")
                        print(f"      🔍 Por que Ponyta foi selecionado?")
                        print(f"      🔍 Ponyta type1={type1} ({type1_name}), type2={type2} ({type2_name if type2_name else 'None'})")
                        print(f"      🔍 Koga party types: {[type_names.get(t, f'Type_{t}') for t in party_types]}")
                        
                        # Verificar se há erro na leitura de tipos
                        if type1 == 3 or type2 == 3:  # POISON = 3
                            print(f"      ⚠️ PONYTA TEM TIPO POISON NO PROJETO! Isso pode estar incorreto.")
                        elif type1 == 9:  # FIRE = 9
                            print(f"      ❌ PONYTA É FIRE TYPE - não deveria ser selecionado para Koga")
                
        except Exception as e:
            print(f"   ❌ Erro na simulação: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_ponyta_in_base_stats():
    """Verifica os dados de Ponyta no Base_Stats.c"""
    
    print(f"\n🔍 VERIFICAÇÃO: Dados de Ponyta no Base_Stats.c")
    print("=" * 50)
    
    try:
        # Procurar Ponyta no Base_Stats.c
        base_stats_file = "src/Base_Stats.c"
        
        if not os.path.exists(base_stats_file):
            print(f"❌ Arquivo {base_stats_file} não encontrado")
            return
        
        with open(base_stats_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Procurar por Ponyta (species 77)
        lines = content.split('\n')
        ponyta_found = False
        
        for i, line in enumerate(lines):
            if '#0077 - Ponyta' in line or 'SPECIES_PONYTA' in line:
                ponyta_found = True
                print(f"   ✅ Ponyta encontrado na linha {i+1}")
                
                # Mostrar contexto (5 linhas antes e depois)
                start = max(0, i-5)
                end = min(len(lines), i+10)
                
                print(f"   📋 Contexto do Base_Stats.c:")
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}{j+1:4d}: {lines[j]}")
                
                break
        
        if not ponyta_found:
            print(f"   ❌ Ponyta não encontrado no Base_Stats.c")
            
            # Procurar por species 77 de forma mais ampla
            for i, line in enumerate(lines):
                if '77' in line and ('BaseStats' in line or 'Species' in line):
                    print(f"   🔍 Possível referência na linha {i+1}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar Base_Stats.c: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_koga_ponyta_issue()
    check_ponyta_in_base_stats()
