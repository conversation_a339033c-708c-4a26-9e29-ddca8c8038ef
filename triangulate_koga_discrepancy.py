#!/usr/bin/env python3
"""
Triangulação crítica: Por que há discrepância entre logs e ROM final
- Logs mostram: SKUNTANK (#1239) e NAGANADEL (#1075)
- ROM real contém: PONYTA, GROWLITHE, QWILFISH (#211)
"""

import sys
import struct
sys.path.append('scripts')

def triangulate_koga_discrepancy():
    """Triangulação completa da discrepância do Koga"""
    
    print("🔍 TRIANGULAÇÃO CRÍTICA: DISCREPÂNCIA KOGA")
    print("=" * 60)
    
    try:
        from insert import LoadProjectPokemonDatabase, ConvertTypeNumberToName
        
        # STEP 1: Verificar mapeamento de Species IDs
        print("📊 STEP 1: Verificando mapeamento de Species IDs")
        print("-" * 40)
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # IDs reportados vs esperados
        species_to_check = {
            "QWILFISH": 211,      # 0xD3 = 211 decimal
            "SKUNTANK": 1239,     # Logged
            "NAGANADEL": 1075,    # Logged
            "PONYTA": 77,         # Aparece na ROM
            "GROWLITHE": 58,      # Aparece na ROM
            "GASTLY": 92,         # Original que pode estar sendo modificado
        }
        
        for name, species_id in species_to_check.items():
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                actual_name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                type1_name = ConvertTypeNumberToName(type1) if type1 else "None"
                type2_name = ConvertTypeNumberToName(type2) if type2 else "None"
                
                name_match = "✅" if actual_name.upper() == name else "❌"
                
                print(f"  {name} (#{species_id}):")
                print(f"    Expected: {name}")
                print(f"    Actual: {actual_name} {name_match}")
                print(f"    Types: {type1_name}/{type2_name}")
                print()
            else:
                print(f"  {name} (#{species_id}): NOT FOUND in project database")
                print()
        
        # STEP 2: Verificar se há conversão de IDs
        print("🔄 STEP 2: Verificando conversão de Species IDs")
        print("-" * 40)
        
        # Check if there's any ID conversion happening
        print("  HIPÓTESES DE CONVERSÃO:")
        print("  1. 🔄 Species ID → National Dex ID conversion")
        print("  2. 🔄 Project ID → ROM ID conversion")
        print("  3. 🔄 Offset calculation errors")
        print("  4. 🔄 Byte order issues (little-endian vs big-endian)")
        print()
        
        # Check hex values
        print("  HEX ANALYSIS:")
        print(f"  QWILFISH: 211 = 0x{211:02X} = 0xD3 ✅ (matches your report)")
        print(f"  SKUNTANK: 1239 = 0x{1239:02X} = 0x4D7")
        print(f"  NAGANADEL: 1075 = 0x{1075:02X} = 0x433")
        print(f"  PONYTA: 77 = 0x{77:02X} = 0x4D")
        print(f"  GROWLITHE: 58 = 0x{58:02X} = 0x3A")
        print()
        
        # STEP 3: Verificar se há múltiplas escritas
        print("📝 STEP 3: Verificando múltiplas escritas na ROM")
        print("-" * 40)
        
        print("  POSSÍVEIS CAUSAS:")
        print("  1. 🔄 Expansion phase escreve SKUNTANK/NAGANADEL")
        print("  2. 🔄 Randomization phase sobrescreve com PONYTA/GROWLITHE")
        print("  3. 🔄 Legendary substitution falha e reverte para fallback")
        print("  4. 🔄 ROM corruption durante escrita")
        print("  5. 🔄 Wrong offset calculation")
        print()
        
        # STEP 4: Verificar se GASTLY está sendo modificado
        print("👻 STEP 4: Verificando modificação dos GASTLY originais")
        print("-" * 40)
        
        print("  ANÁLISE DOS SLOTS ORIGINAIS:")
        print("  Slot 2: GASTLY (#92) → pode estar sendo substituído por PONYTA (#77)")
        print("  Slot 4: GASTLY (#92) → pode estar sendo substituído por GROWLITHE (#58)")
        print()
        print("  EVIDÊNCIA:")
        print("  - PONYTA e GROWLITHE são Fire-type")
        print("  - Não deveriam ser selecionados para Koga (Poison trainer)")
        print("  - Podem estar substituindo GASTLY através de randomização incorreta")
        print()
        
        # STEP 5: Verificar se RANDOMIZE_ONLY_ADDITIONAL está falhando
        print("⚙️ STEP 5: Verificando falha do RANDOMIZE_ONLY_ADDITIONAL")
        print("-" * 40)
        
        from insert import ShouldRandomizeOnlyAdditionalPokemon
        
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()
        print(f"  RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        
        if randomize_only_additional:
            print("  ✅ Configuração está TRUE")
            print("  ❌ MAS slots originais estão sendo modificados!")
            print("  🔍 PROBLEMA: Boundary detection falhou")
        else:
            print("  ❌ Configuração está FALSE - todos Pokemon são randomizados")
        print()
        
        # STEP 6: Verificar Species ID boundaries
        print("🎯 STEP 6: Verificando boundaries de Species IDs")
        print("-" * 40)
        
        print("  QWILFISH ANALYSIS:")
        print(f"  - Species #211 (0xD3) = QWILFISH")
        print(f"  - Você reportou: 'SPECIES_QWILFISH 0xD3'")
        print(f"  - Logs mostram: SKUNTANK (#1239)")
        print(f"  - DISCREPÂNCIA: 211 ≠ 1239")
        print()
        
        print("  POSSÍVEL CAUSA:")
        print("  🔍 Sistema está escrevendo Species #211 (QWILFISH)")
        print("  🔍 Mas logando Species #1239 (SKUNTANK)")
        print("  🔍 Pode haver conversão incorreta entre Project ID e ROM ID")
        print()
        
        # STEP 7: Verificar se há fallback para Gen 1-2 Pokemon
        print("🔄 STEP 7: Verificando fallback para Pokemon antigos")
        print("-" * 40)
        
        print("  PADRÃO IDENTIFICADO:")
        print("  - PONYTA (#77) = Gen 1 Fire-type")
        print("  - GROWLITHE (#58) = Gen 1 Fire-type") 
        print("  - QWILFISH (#211) = Gen 2 Water/Poison-type")
        print()
        print("  HIPÓTESE:")
        print("  🔍 Sistema pode estar fazendo fallback para Pokemon de gerações antigas")
        print("  🔍 Quando seleção de Pokemon modernos falha")
        print("  🔍 QWILFISH tem tipo Poison - pode ser seleção correta mas ID errado")
        print()
        
        # STEP 8: Verificar offset calculations
        print("📐 STEP 8: Verificando cálculos de offset")
        print("-" * 40)
        
        print("  KOGA PARTY STRUCTURE:")
        print("  - Trainer ID: 418")
        print("  - Original party size: 4")
        print("  - Target party size: 6")
        print("  - Additional slots: 2 (slots 5 and 6)")
        print()
        print("  OFFSET CALCULATIONS:")
        print("  - Slot 5 offset: party_offset + (4 * 8) = party_offset + 32")
        print("  - Slot 6 offset: party_offset + (5 * 8) = party_offset + 40")
        print()
        print("  POSSÍVEL PROBLEMA:")
        print("  🔍 Offsets podem estar sendo calculados incorretamente")
        print("  🔍 Escrevendo em slots originais em vez de adicionais")
        print()
        
        # STEP 9: Conclusões
        print("🎯 STEP 9: Conclusões da triangulação")
        print("-" * 40)
        
        print("  PROBLEMAS IDENTIFICADOS:")
        print("  1. 🔄 DISCREPÂNCIA LOGS vs ROM:")
        print("     - Logs: SKUNTANK (#1239), NAGANADEL (#1075)")
        print("     - ROM: PONYTA (#77), GROWLITHE (#58), QWILFISH (#211)")
        print()
        print("  2. 🔄 SLOTS ORIGINAIS MODIFICADOS:")
        print("     - GASTLY (#92) sendo substituído por PONYTA/GROWLITHE")
        print("     - RANDOMIZE_ONLY_ADDITIONAL não está funcionando")
        print()
        print("  3. 🔄 SPECIES ID CONVERSION:")
        print("     - Sistema pode estar convertendo IDs incorretamente")
        print("     - Project Species ID ≠ ROM Species ID")
        print()
        print("  PRÓXIMOS PASSOS:")
        print("  1. 🔍 Adicionar logging detalhado de TODAS as escritas na ROM")
        print("  2. 🛡️ Verificar boundary protection nos slots originais")
        print("  3. 📊 Verificar conversão de Species IDs")
        print("  4. 🔄 Verificar se há múltiplas passadas de randomização")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante triangulação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    triangulate_koga_discrepancy()
