#!/usr/bin/env python3
"""
Debug crítico: Por que boundary protection está falhando
GASTLY slots originais estão sendo modificados quando não deveriam
"""

import sys
import struct
sys.path.append('scripts')

def debug_boundary_protection():
    """Debug da falha do boundary protection"""
    
    print("🛡️ DEBUG: BOUNDARY PROTECTION FAILURE")
    print("=" * 60)
    
    try:
        # STEP 1: Verificar se há outra função randomizando
        print("🔍 STEP 1: Verificando funções de randomização ativas")
        print("-" * 40)
        
        from insert import ShouldRandomizeOnlyAdditionalPokemon
        
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()
        print(f"  RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        
        if randomize_only_additional:
            print("  ✅ Configuração correta")
            print("  ❌ MAS slots originais sendo modificados")
            print("  🔍 PROBLEMA: Boundary protection falhou")
        
        # STEP 2: Verificar se RandomizeAllTrainerPokemonWithCache está sendo chamado
        print("\n🔄 STEP 2: Verificando se função incorreta está sendo chamada")
        print("-" * 40)
        
        print("  WORKFLOW ESPERADO:")
        print("  1. ✅ ShouldRandomizeOnlyAdditionalPokemon() = TRUE")
        print("  2. ✅ Chama RandomizeOnlyAdditionalPokemonWithCache()")
        print("  3. ❌ NÃO deve chamar RandomizeAllTrainerPokemonWithCache()")
        print()
        print("  WORKFLOW REAL:")
        print("  1. ✅ ShouldRandomizeOnlyAdditionalPokemon() = TRUE")
        print("  2. ❌ Mas slots originais estão sendo modificados")
        print("  3. 🔍 SUSPEITA: RandomizeAllTrainerPokemonWithCache sendo chamado")
        
        # STEP 3: Verificar se há problema na verificação da configuração
        print("\n⚙️ STEP 3: Verificando leitura da configuração")
        print("-" * 40)
        
        config_file = "include/wild_encounters_config.h"
        try:
            with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Check exact configuration
            randomize_only_line = None
            for line_num, line in enumerate(content.split('\n'), 1):
                if 'RANDOMIZE_ONLY_ADDITIONAL_POKEMON' in line:
                    randomize_only_line = line.strip()
                    print(f"  Line {line_num}: {randomize_only_line}")
            
            if randomize_only_line:
                if "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content:
                    print("  ✅ Configuração encontrada: TRUE")
                elif "RANDOMIZE_ONLY_ADDITIONAL_POKEMON FALSE" in content:
                    print("  ❌ Configuração encontrada: FALSE")
                else:
                    print("  ⚠️  Configuração ambígua ou comentada")
            else:
                print("  ❌ Configuração não encontrada")
                
        except Exception as e:
            print(f"  ❌ Erro lendo configuração: {e}")
        
        # STEP 4: Verificar se há múltiplas chamadas
        print("\n🔄 STEP 4: Verificando múltiplas chamadas de randomização")
        print("-" * 40)
        
        print("  POSSÍVEIS CENÁRIOS:")
        print("  1. 🔄 RandomizeOnlyAdditionalPokemonWithCache() chamado primeiro")
        print("  2. 🔄 RandomizeAllTrainerPokemonWithCache() chamado depois")
        print("  3. 🔄 Sobrescreve as modificações corretas")
        print()
        print("  EVIDÊNCIA:")
        print("  - Logs mostram seleção correta (QWILFISH_H)")
        print("  - ROM final contém Pokemon incorretos (PONYTA/GROWLITHE)")
        print("  - Indica sobrescrita após logging")
        
        # STEP 5: Verificar se há problema no boundary check
        print("\n🎯 STEP 5: Verificando boundary check específico")
        print("-" * 40)
        
        print("  KOGA BOUNDARY ANALYSIS:")
        print("  - Trainer ID: 418")
        print("  - Original party size: 4 (slots 0, 1, 2, 3)")
        print("  - Current party size: 6 (após expansão)")
        print("  - Additional slots: 2 (slots 4, 5)")
        print()
        print("  BOUNDARY CHECK:")
        print("  - for i in range(original_party_size, current_party_size):")
        print("  - for i in range(4, 6):  # slots 4 and 5")
        print("  - NUNCA deveria processar slots 0, 1, 2, 3")
        print()
        print("  PROBLEMA IDENTIFICADO:")
        print("  🔍 Slots 1 e 3 (GASTLY) estão sendo modificados")
        print("  🔍 Indica que boundary check está falhando")
        print("  🔍 Ou outra função está randomizando todos os slots")
        
        # STEP 6: Verificar se há problema na condição if/else
        print("\n🔀 STEP 6: Verificando condição if/else")
        print("-" * 40)
        
        print("  CÓDIGO CRÍTICO:")
        print("  if randomize_only_additional:")
        print("      result = RandomizeOnlyAdditionalPokemonWithCache(...)")
        print("  else:")
        print("      result = RandomizeAllTrainerPokemonWithCache(...)")
        print()
        print("  POSSÍVEL PROBLEMA:")
        print("  🔍 Condição if pode estar falhando")
        print("  🔍 Executando else em vez de if")
        print("  🔍 Chamando RandomizeAllTrainerPokemonWithCache incorretamente")
        
        # STEP 7: Verificar se há cache corruption
        print("\n💾 STEP 7: Verificando cache corruption")
        print("-" * 40)
        
        print("  CACHE ANALYSIS:")
        print("  - Sistema usa cached trainer data")
        print("  - Original party cached corretamente")
        print("  - Expansion phase adiciona Pokemon corretos")
        print("  - Randomization phase pode estar corrompendo cache")
        print()
        print("  POSSÍVEL PROBLEMA:")
        print("  🔍 Cache sendo modificado durante randomização")
        print("  🔍 Original party size sendo alterado incorretamente")
        print("  🔍 Boundary calculations baseados em dados corrompidos")
        
        # STEP 8: Verificar se há problema de timing
        print("\n⏰ STEP 8: Verificando timing de execução")
        print("-" * 40)
        
        print("  TIMING ANALYSIS:")
        print("  1. ✅ Expansion phase: Adiciona QWILFISH_H e NAGANADEL")
        print("  2. ✅ Logs mostram seleção correta")
        print("  3. ❌ Randomization phase: Modifica slots originais")
        print("  4. ❌ ROM final: PONYTA e GROWLITHE nos slots originais")
        print()
        print("  CONCLUSÃO:")
        print("  🔍 Há uma segunda passada de randomização")
        print("  🔍 Que está ignorando RANDOMIZE_ONLY_ADDITIONAL")
        print("  🔍 E modificando todos os slots da party")
        
        # STEP 9: Soluções propostas
        print("\n🛠️ STEP 9: Soluções propostas")
        print("-" * 40)
        
        print("  SOLUÇÕES IMEDIATAS:")
        print("  1. 🔍 Adicionar logging detalhado em TODAS as escritas na ROM")
        print("  2. 🛡️ Adicionar proteção extra nos slots originais")
        print("  3. 🔄 Verificar se há múltiplas chamadas de randomização")
        print("  4. ⚙️ Verificar se condição if/else está funcionando")
        print()
        print("  DEBUGGING NECESSÁRIO:")
        print("  1. 📝 Log TODAS as modificações de species na ROM")
        print("  2. 🎯 Log qual função está fazendo cada modificação")
        print("  3. 🔍 Verificar se RandomizeAllTrainerPokemonWithCache está sendo chamado")
        print("  4. 🛡️ Adicionar assert() nos boundary checks")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_boundary_protection()
