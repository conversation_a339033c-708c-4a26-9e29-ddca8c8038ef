#!/usr/bin/env python3
"""
Debug crítico: Investigar mapeamento de IDs entre ROM e Projeto
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_id_mapping():
    """Investiga o mapeamento de IDs"""
    
    print("🚨 DEBUG CRÍTICO: Mapeamento de IDs ROM vs Projeto")
    print("=" * 60)
    
    try:
        from insert import (
            LoadProjectPokemonDatabase,
            LoadSpeciesMapping,
            ReadAllTrainerDataFromOriginalROM
        )
        
        # Limpar cache para aplicar correções
        import insert
        insert._project_pokemon_cache = None
        
        # Carregar dados do projeto com nomes corrigidos
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        print(f"🔍 INVESTIGAÇÃO 1: Verificar nomes após correção")
        print("-" * 50)
        
        # Verificar IDs específicos mencionados
        test_ids = [3, 58, 77, 92, 109, 643, 696, 1415]
        
        for species_id in test_ids:
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                print(f"   Species #{species_id:4d}: {name:15s} (Type1={type1}, Type2={type2})")
            else:
                print(f"   Species #{species_id:4d}: NÃO ENCONTRADO no projeto")
        
        print(f"\n🔍 INVESTIGAÇÃO 2: Verificar Species Mapping")
        print("-" * 50)
        
        # Carregar mapeamento de species
        species_mapping = LoadSpeciesMapping()
        
        # Procurar por Pokémon específicos
        target_pokemon = ['KOFFING', 'MUK', 'WEEZING', 'GASTLY', 'PONYTA', 'GROWLITHE', 'VENUSAUR', 'RESHIRAM']
        
        for pokemon_name in target_pokemon:
            if pokemon_name in species_mapping:
                species_id = species_mapping[pokemon_name]
                print(f"   {pokemon_name:12s} -> Species #{species_id}")
                
                # Verificar se existe no projeto
                if species_id in project_pokemon_data:
                    pokemon_data = project_pokemon_data[species_id]
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')
                    print(f"      ✅ Encontrado no projeto (Type1={type1}, Type2={type2})")
                else:
                    print(f"      ❌ NÃO encontrado no projeto")
            else:
                print(f"   {pokemon_name:12s} -> NÃO ENCONTRADO no mapping")
        
        print(f"\n🔍 INVESTIGAÇÃO 3: Party Original vs ROM")
        print("-" * 50)
        
        # Comparar party original com ROM atual
        original_cache = ReadAllTrainerDataFromOriginalROM()
        if original_cache and 'trainers' in original_cache:
            koga_id = 418
            if koga_id in original_cache['trainers']:
                koga_info = original_cache['trainers'][koga_id]
                original_party = koga_info['original_party']
                
                print(f"   Party original de Koga (cache):")
                for i, pokemon in enumerate(original_party):
                    species = pokemon.get('species', 'Unknown')
                    level = pokemon.get('level', 'Unknown')
                    
                    # Verificar nome no projeto
                    if species in project_pokemon_data:
                        pokemon_data = project_pokemon_data[species]
                        name = pokemon_data.get('name', 'Unknown')
                        type1 = pokemon_data.get('type1')
                        type2 = pokemon_data.get('type2')
                        print(f"      Slot {i+1}: Species #{species:3d} ({name:12s}) Level {level} (Type1={type1}, Type2={type2})")
                    else:
                        print(f"      Slot {i+1}: Species #{species:3d} (NÃO ENCONTRADO) Level {level}")
        
        print(f"\n🔍 INVESTIGAÇÃO 4: Verificar Pokédex Nacional vs Species ID")
        print("-" * 50)
        
        # Mapear Pokédex Nacional para Species ID
        national_to_species = {}
        species_to_national = {}
        
        # Pokémon conhecidos da Gen 1-3 (Pokédex Nacional = Species ID)
        gen1_3_pokemon = {
            77: 'PONYTA',      # Pokédex #77 = Species #77?
            58: 'GROWLITHE',   # Pokédex #58 = Species #58?
            109: 'KOFFING',    # Pokédex #109 = Species #109?
            89: 'MUK',         # Pokédex #89 = Species #89?
            110: 'WEEZING',    # Pokédex #110 = Species #110?
            92: 'GASTLY',      # Pokédex #92 = Species #92?
            3: 'VENUSAUR',     # Pokédex #3 = Species #3?
        }
        
        print(f"   Verificando mapeamento Gen 1-3:")
        for national_num, expected_name in gen1_3_pokemon.items():
            # Verificar se Species ID = Pokédex Nacional
            if national_num in project_pokemon_data:
                pokemon_data = project_pokemon_data[national_num]
                actual_name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                if actual_name.upper() == expected_name:
                    print(f"      ✅ #{national_num:3d}: {expected_name:12s} = Species #{national_num} (Type1={type1}, Type2={type2})")
                else:
                    print(f"      ❌ #{national_num:3d}: Esperado {expected_name:12s}, encontrado {actual_name:12s}")
            else:
                print(f"      ❌ #{national_num:3d}: {expected_name:12s} NÃO ENCONTRADO como Species #{national_num}")
        
        print(f"\n🔍 INVESTIGAÇÃO 5: Procurar Pokémon por nome")
        print("-" * 50)
        
        # Procurar Pokémon por nome no projeto
        target_names = ['PONYTA', 'GROWLITHE', 'KOFFING', 'MUK', 'WEEZING', 'GASTLY', 'VENUSAUR']
        
        for target_name in target_names:
            found_species = []
            for species_id, pokemon_data in project_pokemon_data.items():
                name = pokemon_data.get('name', '').upper()
                if target_name in name:
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')
                    found_species.append((species_id, name, type1, type2))
            
            if found_species:
                print(f"   {target_name:12s}:")
                for species_id, name, type1, type2 in found_species:
                    print(f"      Species #{species_id:4d}: {name:15s} (Type1={type1}, Type2={type2})")
            else:
                print(f"   {target_name:12s}: NÃO ENCONTRADO")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_rom_original_data():
    """Verifica dados originais na ROM BPRE0.gba"""
    
    print(f"\n🔍 INVESTIGAÇÃO 6: Dados Originais na ROM BPRE0.gba")
    print("-" * 50)
    
    try:
        from insert import FindTrainerTable
        
        # Verificar ROM original
        if os.path.exists("BPRE0.gba"):
            print(f"   ✅ ROM original encontrada: BPRE0.gba")
            
            with open("BPRE0.gba", "rb") as rom:
                trainer_table_offset = FindTrainerTable(rom)
                koga_id = 418
                
                # Ler dados do trainer Koga na ROM original
                trainer_offset = trainer_table_offset + (koga_id * 40)
                rom.seek(trainer_offset)
                trainer_data = rom.read(40)
                
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                party_offset = party_ptr - 0x08000000
                
                print(f"   Koga na ROM original - Party size: {party_size}, offset: 0x{party_offset:08X}")
                
                # Ler party original
                rom.seek(party_offset)
                party_data = rom.read(party_size * 8)
                
                print(f"   Party original na ROM BPRE0.gba:")
                for i in range(party_size):
                    pokemon_offset = i * 8
                    pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                    
                    if len(pokemon_data) >= 8:
                        level = pokemon_data[2]
                        species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                        print(f"      Slot {i+1}: Species #{species_id:3d} Level {level}")
        else:
            print(f"   ❌ ROM original não encontrada: BPRE0.gba")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar ROM original: {e}")
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar investigações
    debug_id_mapping()
    check_rom_original_data()
