#!/usr/bin/env python3
"""
Análise completa do workflow para identificar onde ROM é usada em vez do projeto
EVIDÊNCIA: Ponyta aparece no time do Koga - sistema usando ROM em vez do projeto
"""

import sys
import os

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def analyze_complete_workflow():
    """Analisa todo o workflow de randomização"""
    
    print("🔍 ANÁLISE COMPLETA DO WORKFLOW DE RANDOMIZAÇÃO")
    print("=" * 70)
    
    try:
        # Verificar todas as funções que podem estar usando ROM
        print("🎯 FUNÇÕES QUE PODEM USAR ROM EM VEZ DO PROJETO:")
        print("-" * 70)
        
        # Ler o arquivo insert.py
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Procurar por funções que usam pokemon_stats_offset ou ROM
        rom_usage_functions = []
        
        current_function = None
        for i, line in enumerate(lines):
            # Detectar início de função
            if line.strip().startswith('def '):
                current_function = line.strip()
            
            # Procurar por uso de ROM
            if current_function and any(pattern in line for pattern in [
                'pokemon_stats_offset', 'GetPokemonTypesFromOffset', 'GetPokemonBSTFromOffset',
                'ReadPokemonTypesFromROM', 'ReadPokemonBSTFromROM', 'rom.seek'
            ]):
                if current_function not in rom_usage_functions:
                    rom_usage_functions.append((current_function, i+1))
        
        print(f"📋 FUNÇÕES QUE USAM ROM ({len(rom_usage_functions)} encontradas):")
        for func, line_num in rom_usage_functions:
            print(f"   Linha {line_num:4d}: {func}")
        
        # Verificar especificamente as funções de randomização
        print(f"\n🎯 ANÁLISE ESPECÍFICA DAS FUNÇÕES DE RANDOMIZAÇÃO:")
        print("-" * 70)
        
        critical_functions = [
            'SelectAdditionalPokemonWithAppropriateTypes',
            'SelectPokemonFromProjectDatabase', 
            'RandomizeOnlyAdditionalPokemonWithCache',
            'ExpandTrainerPartyWithCachedData',
            'FindPokemonByType',
            'FindPokemonByTypeWithCompatibility'
        ]
        
        for func_name in critical_functions:
            print(f"\n📋 {func_name}:")
            analyze_function_rom_usage(lines, func_name)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_function_rom_usage(lines, function_name):
    """Analisa uma função específica para uso de ROM"""
    
    in_function = False
    function_lines = []
    
    for i, line in enumerate(lines):
        if f"def {function_name}" in line:
            in_function = True
            function_lines = []
        
        if in_function:
            function_lines.append((i+1, line))
            
            # Verificar padrões problemáticos
            if any(pattern in line for pattern in [
                'pokemon_stats_offset', 'GetPokemonTypesFromOffset', 'GetPokemonBSTFromOffset',
                'ReadPokemonTypesFromROM', 'ReadPokemonBSTFromROM'
            ]):
                print(f"      🚨 Linha {i+1}: USA ROM - {line.strip()}")
            elif any(pattern in line for pattern in [
                'project_pokemon_data', 'LoadProjectPokemonDatabase'
            ]):
                print(f"      ✅ Linha {i+1}: USA PROJETO - {line.strip()}")
            
            # Parar quando sair da função
            if line.strip() and not line.startswith(' ') and not line.startswith('\t') and i > 0:
                if in_function and "def " in line and f"def {function_name}" not in line:
                    break

def trace_koga_workflow():
    """Rastreia especificamente o workflow do Koga"""
    
    print(f"\n🎯 RASTREAMENTO ESPECÍFICO: Workflow do Koga")
    print("=" * 60)
    
    try:
        from insert import (
            ReadAllTrainerDataFromOriginalROM,
            ExpandTrainerPartyWithCachedData,
            RandomizeTrainerWithCachedData
        )
        
        # Ler dados originais
        original_trainer_cache = ReadAllTrainerDataFromOriginalROM()
        if not original_trainer_cache:
            print("❌ Falha ao ler dados cached")
            return
        
        cached_trainers = original_trainer_cache['trainers']
        koga_id = 418
        
        if koga_id not in cached_trainers:
            print(f"❌ Koga não encontrado")
            return
        
        koga_data = cached_trainers[koga_id]
        
        print(f"📋 DADOS ORIGINAIS DE KOGA:")
        print(f"   Trainer ID: {koga_id}")
        print(f"   Trainer class: {koga_data['trainer_class']}")
        print(f"   Party original: {len(koga_data['original_party'])} Pokémon")
        
        for i, pokemon in enumerate(koga_data['original_party']):
            species = pokemon.get('species', 'Unknown')
            level = pokemon.get('level', 'Unknown')
            print(f"      Slot {i+1}: #{species} Level {level}")
        
        # Verificar se Ponyta está na party original
        original_species = [p.get('species') for p in koga_data['original_party']]
        if 77 in original_species:
            print(f"   🚨 PONYTA ENCONTRADA NA PARTY ORIGINAL!")
            print(f"   🔍 Isso significa que Ponyta vem da ROM original, não da randomização!")
        else:
            print(f"   ✅ Ponyta NÃO está na party original")
        
        # Simular workflow completo
        print(f"\n🔄 SIMULAÇÃO DO WORKFLOW COMPLETO:")
        print("-" * 40)
        
        # STEP 1: Party Expansion
        print(f"1. 📈 Party Expansion:")
        print(f"   - Original size: {len(koga_data['original_party'])}")
        print(f"   - Boss trainers: +3 Pokémon")
        print(f"   - Expected final size: {min(6, len(koga_data['original_party']) + 3)}")
        
        # STEP 2: Randomization
        print(f"\n2. 🎲 Randomization:")
        print(f"   - RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE")
        print(f"   - Slots originais (1-4): NÃO randomizados")
        print(f"   - Slots adicionais (5-6): Randomizados com dados do PROJETO")
        
        # STEP 3: Verificar se há outra função interferindo
        print(f"\n3. 🔍 Possíveis Interferências:")
        print(f"   - Função de randomização geral")
        print(f"   - Função de correção de dados")
        print(f"   - Função de aplicação de temas")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante rastreamento: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_all_randomization_calls():
    """Verifica todas as chamadas de randomização no código"""
    
    print(f"\n🔍 VERIFICAÇÃO: Todas as Chamadas de Randomização")
    print("=" * 60)
    
    try:
        # Ler o arquivo insert.py
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Procurar por todas as chamadas de funções de randomização
        randomization_calls = []
        
        for i, line in enumerate(lines):
            if any(pattern in line for pattern in [
                'RandomizeTrainer', 'RandomizeAllTrainer', 'RandomizeOnlyAdditional',
                'ExpandTrainerParty', 'SelectAdditionalPokemon', 'SelectPokemonFrom'
            ]):
                if not line.strip().startswith('def ') and not line.strip().startswith('#'):
                    randomization_calls.append((i+1, line.strip()))
        
        print(f"📋 CHAMADAS DE RANDOMIZAÇÃO ENCONTRADAS ({len(randomization_calls)}):")
        for line_num, call in randomization_calls:
            print(f"   Linha {line_num:4d}: {call}")
        
        # Verificar ordem de execução
        print(f"\n🔄 ORDEM DE EXECUÇÃO SUSPEITA:")
        print(f"   1. ExpandTrainerPartyWithCachedData (adiciona slots)")
        print(f"   2. RandomizeTrainerWithCachedData (randomiza baseado em config)")
        print(f"   3. ??? FUNÇÃO ADICIONAL ??? (pode estar sobrescrevendo)")
        
        # Procurar por funções que podem estar sendo chamadas após
        print(f"\n🚨 FUNÇÕES SUSPEITAS QUE PODEM SOBRESCREVER:")
        
        suspicious_patterns = [
            'ApplyTrainerPokemonRandomization',
            'RandomizeTrainerParty',
            'RandomizeAllTrainer',
            'CorrectTrainerData'
        ]
        
        for pattern in suspicious_patterns:
            for i, line in enumerate(lines):
                if pattern in line and not line.strip().startswith('def ') and not line.strip().startswith('#'):
                    print(f"   🔍 Linha {i+1:4d}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

def identify_rom_vs_project_usage():
    """Identifica onde ROM é usada em vez do projeto"""
    
    print(f"\n🎯 IDENTIFICAÇÃO: ROM vs Projeto")
    print("=" * 50)
    
    try:
        print(f"📋 PADRÕES PROBLEMÁTICOS:")
        print(f"   🚨 ROM: pokemon_stats_offset, GetPokemonTypesFromOffset, ReadPokemonTypesFromROM")
        print(f"   ✅ PROJETO: project_pokemon_data, LoadProjectPokemonDatabase")
        
        print(f"\n🔍 HIPÓTESES PRINCIPAIS:")
        print(f"   1. 🚨 Função usa ROM para validação/verificação")
        print(f"   2. 🚨 Função usa ROM para BST/tipos em vez do projeto")
        print(f"   3. 🚨 Função de fallback usa ROM quando projeto falha")
        print(f"   4. 🚨 Função de correção sobrescreve seleções do projeto")
        
        print(f"\n💡 SOLUÇÕES NECESSÁRIAS:")
        print(f"   1. Substituir TODAS as chamadas ROM por projeto")
        print(f"   2. Remover funções de fallback que usam ROM")
        print(f"   3. Garantir que projeto é SEMPRE a fonte de dados")
        print(f"   4. Adicionar logs para rastrear origem dos dados")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante identificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar análise completa
    analyze_complete_workflow()
    trace_koga_workflow()
    check_all_randomization_calls()
    identify_rom_vs_project_usage()
