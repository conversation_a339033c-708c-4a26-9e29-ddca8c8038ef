#!/usr/bin/env python3
"""
Triangulação do problema do Koga: Por que Ponyta e Growlithe aparecem na ROM
quando o sistema está logando Budew e Naganadel?
"""

import sys
import struct
sys.path.append('scripts')

def triangulate_koga_issue():
    """Análise triangulada do problema do Koga"""
    
    print("🔍 TRIANGULAÇÃO: PROBLEMA DO KOGA")
    print("=" * 60)
    
    # STEP 1: Verificar o que está realmente na ROM
    print("📖 STEP 1: Lendo dados REAIS da ROM compilada")
    print("-" * 40)
    
    try:
        with open('test.gba', 'rb') as rom:
            # <PERSON><PERSON>'s trainer offset
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            # Read Ko<PERSON>'s trainer data
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
            party_file_offset = party_pointer - 0x08000000 if party_pointer >= 0x08000000 else party_pointer
            
            print(f"  Koga Trainer ID: {koga_trainer_id}")
            print(f"  Party Size: {party_size}")
            print(f"  Party Pointer: 0x{party_pointer:08X}")
            print(f"  Party File Offset: 0x{party_file_offset:08X}")
            
            # Read actual party data
            rom.seek(party_file_offset)
            actual_party = []
            
            for i in range(party_size):
                pokemon_data = rom.read(8)
                if len(pokemon_data) < 8:
                    break
                
                iv = pokemon_data[0]
                level = pokemon_data[2]
                species = struct.unpack('<H', pokemon_data[4:6])[0]
                held_item = struct.unpack('<H', pokemon_data[6:8])[0]
                
                actual_party.append({
                    'slot': i + 1,
                    'species': species,
                    'level': level,
                    'iv': iv,
                    'held_item': held_item
                })
                
                print(f"  Slot {i+1}: Species #{species}, Level {level}, IV {iv}")
            
            print()
            
    except Exception as e:
        print(f"❌ Erro lendo ROM: {e}")
        return False
    
    # STEP 2: Verificar limitações de party size
    print("📏 STEP 2: Verificando limitações de party size")
    print("-" * 40)
    
    # Check if there are any party size limitations for Koga specifically
    max_party_size = 6
    original_party_size = 4  # Koga's original party
    expected_additional = 2  # Should add 2 more (4 + 2 = 6)
    
    print(f"  Original party size: {original_party_size}")
    print(f"  Expected additional: {expected_additional}")
    print(f"  Expected final size: {original_party_size + expected_additional}")
    print(f"  Actual final size: {party_size}")
    print(f"  Max party size limit: {max_party_size}")
    
    if party_size != (original_party_size + expected_additional):
        print(f"  ⚠️  DISCREPÂNCIA: Tamanho esperado vs real!")
    else:
        print(f"  ✅ Tamanho da party está correto")
    
    print()
    
    # STEP 3: Verificar se há múltiplas escritas na mesma posição
    print("🔄 STEP 3: Verificando múltiplas escritas nos slots")
    print("-" * 40)
    
    # Check if there are multiple writes to the same slots
    # This could explain why logged Pokemon differ from final ROM data
    
    # Simulate the expansion process
    try:
        from insert import LoadProjectPokemonDatabase, ConvertTypeNumberToName
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # Check what Ponyta and Growlithe are in the project
        ponyta_id = 77  # Ponyta
        growlithe_id = 58  # Growlithe
        budew_id = 459  # Budew (logged)
        naganadel_id = 1075  # Naganadel (logged)
        
        pokemon_to_check = [
            ("PONYTA", ponyta_id),
            ("GROWLITHE", growlithe_id), 
            ("BUDEW", budew_id),
            ("NAGANADEL", naganadel_id)
        ]
        
        for name, species_id in pokemon_to_check:
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                type1_name = ConvertTypeNumberToName(type1) if type1 else "None"
                type2_name = ConvertTypeNumberToName(type2) if type2 else "None"
                bst = pokemon_data.get('bst', 0)
                
                print(f"  {name} (#{species_id}):")
                print(f"    Types: {type1_name}/{type2_name}")
                print(f"    BST: {bst}")
                
                # Check if it matches Koga's type theme
                poison_type = 3  # POISON = 3
                if type1 == poison_type or type2 == poison_type:
                    print(f"    ✅ Matches Koga's Poison theme")
                else:
                    print(f"    ❌ Does NOT match Koga's Poison theme")
                print()
            else:
                print(f"  {name} (#{species_id}): NOT FOUND in project database")
                print()
        
    except Exception as e:
        print(f"❌ Erro verificando Pokemon: {e}")
    
    # STEP 4: Verificar se há sobrescrita de dados
    print("📝 STEP 4: Verificando possível sobrescrita de dados")
    print("-" * 40)
    
    # Check if there's data overwriting happening
    # This could be due to:
    # 1. Multiple randomization passes
    # 2. Different functions writing to same slots
    # 3. ROM data corruption
    
    print("  Possíveis causas da discrepância:")
    print("  1. ✅ Múltiplas passadas de randomização")
    print("  2. ✅ Funções diferentes escrevendo nos mesmos slots")
    print("  3. ✅ Dados sendo sobrescritos após logging")
    print("  4. ✅ Configuração RANDOMIZE_ONLY_ADDITIONAL não sendo respeitada")
    print()
    
    # STEP 5: Verificar se os slots 2 e 4 (Gastly) estão sendo modificados
    print("👻 STEP 5: Verificando se Gastly slots estão sendo modificados")
    print("-" * 40)
    
    # Original Koga party should be:
    # Slot 1: Koffing (#109)
    # Slot 2: Gastly (#92) 
    # Slot 3: Muk (#89)
    # Slot 4: Gastly (#92)
    # Slots 5-6: Should be additional Pokemon
    
    original_gastly_id = 92
    
    for pokemon in actual_party:
        slot = pokemon['slot']
        species = pokemon['species']
        
        if slot in [2, 4]:  # Original Gastly slots
            if species != original_gastly_id:
                print(f"  ⚠️  SLOT {slot}: Expected Gastly (#{original_gastly_id}), found #{species}")
                print(f"      This indicates ORIGINAL party modification!")
            else:
                print(f"  ✅ SLOT {slot}: Gastly preserved correctly")
        elif slot in [5, 6]:  # Additional slots
            print(f"  📝 SLOT {slot}: Additional Pokemon #{species}")
    
    print()
    
    # STEP 6: Conclusões
    print("🎯 STEP 6: Conclusões da triangulação")
    print("-" * 40)
    
    print("  HIPÓTESES PRINCIPAIS:")
    print("  1. 🔄 Sistema está fazendo múltiplas passadas de randomização")
    print("  2. 📝 Logs mostram seleção inicial, mas dados são sobrescritos depois")
    print("  3. ⚙️  RANDOMIZE_ONLY_ADDITIONAL não está funcionando corretamente")
    print("  4. 👻 Slots originais (Gastly) estão sendo modificados incorretamente")
    print()
    
    print("  PRÓXIMOS PASSOS:")
    print("  1. 🔍 Verificar se RandomizeOnlyAdditionalPokemonWithCache está sendo chamado")
    print("  2. 📊 Verificar se há múltiplas escritas nos mesmos offsets")
    print("  3. 🛡️  Adicionar proteção extra para slots originais")
    print("  4. 📋 Adicionar logging detalhado de todas as escritas na ROM")
    
    return True

if __name__ == "__main__":
    triangulate_koga_issue()
