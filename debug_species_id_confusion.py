#!/usr/bin/env python3
"""
Debug: Investigar confusão entre Species IDs - por que Ponyta aparece quando Species_1 é selecionado?
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_species_id_confusion():
    """Investiga confusão entre Species IDs"""
    
    print("🔍 DEBUG: Confusão entre Species IDs")
    print("=" * 50)
    
    try:
        from insert import FindTrainerTable
        
        # Verificar Koga na ROM atual
        koga_id = 418
        
        print(f"🎯 VERIFICAÇÃO DIRETA NA ROM ATUAL (test.gba):")
        
        with open("test.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Koga party size: {party_size}")
            print(f"   Koga party offset: 0x{party_offset:08X}")
            
            # Ler party atual
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"\n📋 PARTY ATUAL DE KOGA NA ROM:")
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) < 8:
                    continue
                
                level = pokemon_data[2]
                species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                
                print(f"   Slot {i+1}: Level {level:2d}, Species #{species_id:4d} (0x{species_id:04X})")
                
                # Verificar se é Ponyta
                if species_id == 77:  # 0x4D
                    print(f"      🚨 PONYTA ENCONTRADO! Species #{species_id} = 0x{species_id:04X}")
                elif species_id == 1:
                    print(f"      🌱 BULBASAUR encontrado (Species #1)")
        
        # Verificar se há problema na conversão de Species IDs
        print(f"\n🔍 VERIFICAÇÃO DE SPECIES IDS:")
        
        # Verificar alguns Species IDs importantes
        important_species = [1, 77, 167, 643]  # Bulbasaur, Ponyta, Spinarak, Reshiram
        
        from insert import LoadProjectPokemonDatabase
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        for species in important_species:
            if species in project_pokemon_data:
                pokemon_data = project_pokemon_data[species]
                name = pokemon_data.get('name', f'Species_{species}')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                type_names = {
                    0: "NORMAL", 1: "FIGHTING", 2: "FLYING", 3: "POISON", 4: "GROUND",
                    5: "ROCK", 6: "BUG", 7: "GHOST", 8: "STEEL", 9: "FIRE",
                    10: "WATER", 11: "GRASS", 12: "ELECTRIC", 13: "PSYCHIC", 14: "ICE",
                    15: "DRAGON", 16: "DARK", 17: "FAIRY", 18: "ROOSTLESS", 19: "BLANK"
                }
                
                type1_name = type_names.get(type1, f'Type_{type1}') if type1 is not None else 'None'
                type2_name = type_names.get(type2, f'Type_{type2}') if type2 is not None and type2 != type1 else None
                
                type_str = f"{type1_name}" + (f"/{type2_name}" if type2_name else "")
                
                print(f"   #{species:3d} (0x{species:04X}): {name:15s} ({type_str})")
        
        # HIPÓTESE: Verificar se há problema na escrita dos Species IDs
        print(f"\n🧪 HIPÓTESES SOBRE O PROBLEMA:")
        print(f"   1. 🔍 ESCRITA INCORRETA: Sistema escreve Species #1 mas ROM interpreta como #77")
        print(f"   2. 🔍 LEITURA INCORRETA: ROM tem Species #77 mas sistema lê como #1")
        print(f"   3. 🔍 OFFSET INCORRETO: Escrita em offset errado")
        print(f"   4. 🔍 ENDIANNESS: Problema na conversão little/big endian")
        print(f"   5. 🔍 CACHE DESATUALIZADO: Dados cached não refletem ROM atual")
        
        # Verificar se 0x4D (77) pode ser confundido com algo
        print(f"\n🔍 ANÁLISE DE 0x4D (77 decimal):")
        print(f"   Decimal: 77")
        print(f"   Hexadecimal: 0x4D")
        print(f"   Binário: {bin(77)}")
        print(f"   ASCII: '{chr(77)}' (M)")
        
        # Verificar se 0x01 pode ser confundido
        print(f"\n🔍 ANÁLISE DE 0x01 (1 decimal):")
        print(f"   Decimal: 1")
        print(f"   Hexadecimal: 0x01")
        print(f"   Binário: {bin(1)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_rom_vs_logs():
    """Compara dados da ROM com logs de compilação"""
    
    print(f"\n🔍 COMPARAÇÃO: ROM vs Logs de Compilação")
    print("=" * 50)
    
    try:
        print(f"📋 LOGS DE COMPILAÇÃO MOSTRARAM:")
        print(f"   🔍 DEBUG KOGA: Selecionados 2 Pokémon: [1, 1]")
        print(f"   🔍 DEBUG KOGA: Inserindo #1 no slot 5")
        print(f"   🔍 DEBUG KOGA: Inserindo #1 no slot 6")
        print(f"   🏆 KOGA: Added #167, #643")
        
        print(f"\n🎮 OBSERVAÇÃO NO JOGO:")
        print(f"   🐴 Koga tem Ponyta (#77 = 0x4D)")
        
        print(f"\n🚨 DISCREPÂNCIAS IDENTIFICADAS:")
        print(f"   1. ❌ Logs mostram Species #1 sendo inserido")
        print(f"   2. ❌ Logs finais mostram #167, #643")
        print(f"   3. ❌ Jogo mostra Ponyta #77")
        
        print(f"\n💡 POSSÍVEIS EXPLICAÇÕES:")
        print(f"   1. 🔍 LOGS INCORRETOS: Debug logs não refletem inserção real")
        print(f"   2. 🔍 MÚLTIPLAS PASSADAS: Sistema faz várias passadas e sobrescreve")
        print(f"   3. 🔍 FUNÇÃO DIFERENTE: Outra função está modificando Koga")
        print(f"   4. 🔍 CACHE PROBLEM: Dados cached não são usados na inserção final")
        print(f"   5. 🔍 SPECIES MAPPING: Há mapeamento incorreto entre IDs internos e ROM")
        
        print(f"\n🎯 PRÓXIMOS PASSOS SUGERIDOS:")
        print(f"   1. Verificar se há múltiplas funções modificando trainers")
        print(f"   2. Adicionar logs na escrita real da ROM")
        print(f"   3. Verificar se há mapeamento de Species IDs")
        print(f"   4. Comparar dados antes e depois da compilação")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante comparação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_species_id_confusion()
    check_rom_vs_logs()
