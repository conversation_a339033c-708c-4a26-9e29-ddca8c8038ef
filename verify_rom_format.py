#!/usr/bin/env python3
"""
Verificação do formato da ROM: Pode haver interpretação incorreta dos bytes
"""

import sys
import struct
sys.path.append('scripts')

def verify_rom_format():
    """Verifica se há problema na interpretação do formato da ROM"""
    
    print("🔍 VERIFICAÇÃO: FORMATO DA ROM")
    print("=" * 60)
    
    try:
        # Check if the ROM format interpretation is correct
        print("📖 Verificando interpretação dos bytes da ROM...")
        
        # Open both ROMs for comparison
        with open('test.gba', 'rb') as modified_rom, open('BPRE0.gba', 'rb') as original_rom:
            
            # <PERSON><PERSON>'s data
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            print(f"🎯 <PERSON><PERSON><PERSON><PERSON> (Trainer {koga_trainer_id})...")
            print(f"   Trainer offset: 0x{koga_offset:08X}")
            
            # Read trainer data from both ROMs
            original_rom.seek(koga_offset)
            orig_trainer_data = original_rom.read(40)
            
            modified_rom.seek(koga_offset)
            mod_trainer_data = modified_rom.read(40)
            
            # Compare trainer data
            orig_party_size = orig_trainer_data[32]
            mod_party_size = mod_trainer_data[32]
            
            orig_party_pointer = struct.unpack('<I', orig_trainer_data[36:40])[0]
            mod_party_pointer = struct.unpack('<I', mod_trainer_data[36:40])[0]
            
            print(f"📊 TRAINER DATA COMPARISON:")
            print(f"   Original party size: {orig_party_size}")
            print(f"   Modified party size: {mod_party_size}")
            print(f"   Original party pointer: 0x{orig_party_pointer:08X}")
            print(f"   Modified party pointer: 0x{mod_party_pointer:08X}")
            
            # Convert pointers to file offsets
            if orig_party_pointer >= 0x08000000:
                orig_party_offset = orig_party_pointer - 0x08000000
            else:
                orig_party_offset = orig_party_pointer
                
            if mod_party_pointer >= 0x08000000:
                mod_party_offset = mod_party_pointer - 0x08000000
            else:
                mod_party_offset = mod_party_pointer
            
            print(f"   Original party file offset: 0x{orig_party_offset:08X}")
            print(f"   Modified party file offset: 0x{mod_party_offset:08X}")
            print()
            
            # Analyze original party structure
            print("🔍 ANÁLISE DETALHADA DA PARTY ORIGINAL:")
            print("-" * 50)
            
            original_rom.seek(orig_party_offset)
            
            for slot in range(orig_party_size):
                pokemon_offset = orig_party_offset + (slot * 8)
                
                original_rom.seek(pokemon_offset)
                pokemon_data = original_rom.read(8)
                
                if len(pokemon_data) < 8:
                    continue
                
                print(f"Slot {slot + 1} (Original ROM):")
                print(f"   Offset: 0x{pokemon_offset:08X}")
                print(f"   Raw bytes: {pokemon_data.hex()}")
                
                # Try different interpretations
                # Standard interpretation
                iv = pokemon_data[0]
                level = pokemon_data[2]
                species = struct.unpack('<H', pokemon_data[4:6])[0]
                held_item = struct.unpack('<H', pokemon_data[6:8])[0]
                
                print(f"   Standard interpretation:")
                print(f"     IV: {iv}, Level: {level}, Species: #{species}, Item: {held_item}")
                
                # Alternative interpretation 1: Different byte order
                alt_species_1 = struct.unpack('>H', pokemon_data[4:6])[0]  # Big-endian
                alt_item_1 = struct.unpack('>H', pokemon_data[6:8])[0]
                
                print(f"   Alternative 1 (big-endian species/item):")
                print(f"     IV: {iv}, Level: {level}, Species: #{alt_species_1}, Item: {alt_item_1}")
                
                # Alternative interpretation 2: Different field positions
                alt_level_2 = pokemon_data[1]  # Level in different position
                alt_species_2 = struct.unpack('<H', pokemon_data[2:4])[0]  # Species in different position
                
                print(f"   Alternative 2 (different field positions):")
                print(f"     IV: {iv}, Level: {alt_level_2}, Species: #{alt_species_2}")
                
                # Check if any interpretation gives reasonable values
                reasonable_interpretations = []
                
                if 1 <= level <= 100 and 1 <= species <= 1440:
                    reasonable_interpretations.append("Standard")
                
                if 1 <= level <= 100 and 1 <= alt_species_1 <= 1440:
                    reasonable_interpretations.append("Big-endian")
                
                if 1 <= alt_level_2 <= 100 and 1 <= alt_species_2 <= 1440:
                    reasonable_interpretations.append("Different positions")
                
                if reasonable_interpretations:
                    print(f"   ✅ Reasonable interpretations: {', '.join(reasonable_interpretations)}")
                else:
                    print(f"   ❌ No reasonable interpretation found")
                    
                    # Check if this might be a different Pokemon structure
                    print(f"   🔍 Checking for different Pokemon structure...")
                    
                    # Maybe it's a different format entirely
                    # Try interpreting as different structure sizes
                    for struct_size in [6, 10, 12, 16, 20]:
                        if struct_size != 8:
                            alt_offset = orig_party_offset + (slot * struct_size)
                            original_rom.seek(alt_offset)
                            alt_data = original_rom.read(8)  # Still read 8 bytes for comparison
                            
                            if alt_data != pokemon_data:
                                print(f"     Structure size {struct_size}: {alt_data.hex()}")
                
                print()
            
            # Check if the issue is with our Pokemon database
            print("🔍 VERIFICAÇÃO DO DATABASE:")
            print("-" * 30)
            
            try:
                from insert import LoadProjectPokemonDatabase
                project_pokemon_data = LoadProjectPokemonDatabase()
                
                # Check specific Pokemon IDs
                test_species = [77, 58, 92, 109, 89, 211, 1075]
                
                for species_id in test_species:
                    if species_id in project_pokemon_data:
                        pokemon_info = project_pokemon_data[species_id]
                        name = pokemon_info.get('name', 'Unknown')
                        print(f"   Species #{species_id}: {name} ✅")
                    else:
                        print(f"   Species #{species_id}: NOT FOUND ❌")
                
            except Exception as e:
                print(f"   ❌ Erro carregando database: {e}")
            
            # Final hypothesis
            print("\n🎯 HIPÓTESES FINAIS:")
            print("-" * 30)
            print("1. 🔄 ROM original tem formato diferente do esperado")
            print("2. 🔄 Levels >100 são normais nesta ROM (talvez hack/mod)")
            print("3. 🔄 Interpretação de bytes está incorreta")
            print("4. 🔄 Pokemon structure size é diferente de 8 bytes")
            print("5. 🔄 Você está usando ferramenta diferente para verificar")
            
            return True
            
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_rom_format()
