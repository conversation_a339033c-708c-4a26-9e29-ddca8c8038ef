#!/usr/bin/env python3

import os
import subprocess
import shutil
import sys
from datetime import datetime
import _io

OFFSET_TO_PUT = 0x1600000
SOURCE_ROM = "BPRE0.gba"
ROM_NAME = "test.gba"

if sys.platform.startswith('win'):
    PathVar = os.environ.get('Path')
    Paths = PathVar.split(';')
    PATH = ''
    for candidatePath in Paths:
        if 'devkitARM' in candidatePath:
            PATH = candidatePath
            break
    if PATH == '':
        PATH = 'C://devkitPro//devkitARM//bin'
        if os.path.isdir(PATH) is False:
            print('Devkit not found.')
            sys.exit(1)

    PREFIX = 'arm-none-eabi-'
    OBJDUMP = os.path.join(PATH, PREFIX + 'objdump')
    NM = os.path.join(PATH, PREFIX + 'nm')
    AS = os.path.join(PATH, PREFIX + 'as')

else:  # Linux, OSX, etc.
    PREFIX = 'arm-none-eabi-'
    OBJDUMP = (PREFIX + 'objdump')
    NM = (PREFIX + 'nm')
    AS = (PREFIX + 'as')

OUTPUT = 'build/output.bin'
BYTE_REPLACEMENT = 'bytereplacement'
HOOKS = 'hooks'
REPOINTS = 'repoints'
GENERATED_REPOINTS = 'generatedrepoints'
REPOINT_ALL = 'repointall'
ROUTINE_POINTERS = 'routinepointers'
FUNCTION_REWRITES = 'functionrewrites'
SPECIAL_INSERTS = 'special_inserts.asm'
SPECIAL_INSERTS_OUT = 'build/special_inserts.bin'

def ExtractPointer(byteList: [bytes]):
    pointer = 0
    for a in range(len(byteList)):
        pointer += (int(byteList[a])) << (8 * a)

    return pointer

def GetTextSection() -> int:
    try:
        # Dump sections
        out = subprocess.check_output([OBJDUMP, '-t', 'build/linked.o'])
        lines = out.decode().split('\n')

        # Find text section
        text = filter(lambda x: x.strip().endswith('.text'), lines)
        section = (list(text))[0]

        # Get the offset
        offset = int(section.split(' ')[0], 16)
        return offset

    except:
        print("Error: The insertion process could not be completed.\n"
              + "The linker symbol file was not found.")
        sys.exit(1)

def GetSymbols(subtract=0) -> {str: int}:
    out = subprocess.check_output([NM, 'build/linked.o'])
    lines = out.decode().split('\n')

    ret = {}
    for line in lines:
        parts = line.strip().split()

        if len(parts) < 3:
            continue

        if parts[1].lower() not in {'t', 'd'}:
            continue

        offset = int(parts[0], 16)
        ret[parts[2]] = offset - subtract

    return ret

def Hook(rom: _io.BufferedReader, space: int, hookAt: int, register=0):
    # Align 2
    if hookAt & 1:
        hookAt -= 1

    rom.seek(hookAt)

    register &= 7

    if hookAt % 4:
        data = bytes([0x01, 0x48 | register, 0x00 | (register << 3), 0x47, 0x0, 0x0])
    else:
        data = bytes([0x00, 0x48 | register, 0x00 | (register << 3), 0x47])

    space += 0x08000001
    data += (space.to_bytes(4, 'little'))
    rom.write(bytes(data))

def FunctionWrap(rom: _io.BufferedReader, space: int, hookAt: int, numParams: int, isReturning: int):
    # Align 2
    if hookAt & 1:
        hookAt -= 1

    rom.seek(hookAt)
    numParams = numParams - 1

    if numParams < 4:
        data = bytes([0x10, 0xB5, 0x3, 0x4C, 0x0, 0xF0, 0x3, 0xF8, 0x10, 0xBC,
                      (isReturning + 1), 0xBC, (isReturning << 3), 0x47, 0x20, 0x47])
    else:
        k = numParams - 3
        data = bytes([0x10, 0xB5, 0x82, 0xB0])
        for i in range(k + 2):
            data += bytes([i + 2, 0x9C, i, 0x94])
        data += bytes([0x0, 0x9C, numParams - 1, 0x94, 0x1, 0x9C, numParams, 0x94, 0x2, 0xB0, k + 8, 0x4C,
                       0x0, 0xF0, (k << 1) + 13, 0xF8, 0x82, 0xB0, numParams, 0x9C, 0x1, 0x94, numParams - 1,
                       0x9C, 0x0, 0x94])

        for i in reversed(range(k + 2)):
            data += bytes([i, 0x9C, i+2, 0x94])
        data += bytes([0x2, 0xB0, 0x10, 0xBC, isReturning + 1, 0xBC, isReturning << 3, 0x47, 0x20, 0x47])

    space += 0x08000001
    data += (space.to_bytes(4, 'little'))
    rom.write(bytes(data))

def Repoint(rom: _io.BufferedReader, space: int, repointAt: int, slideFactor=0):
    rom.seek(repointAt)

    space += (0x08000000 + slideFactor)
    data = (space.to_bytes(4, 'little'))
    rom.write(bytes(data))

# These offsets contain the word 0x8900000 - the attack data from
# Mr. DS's rombase. In order to maintain as much compatibility as
# possible, the data at these offsets is never modified.
IGNORED_OFFSETS = [0x3986C0, 0x3986EC, 0xDABDF0]

def RealRepoint(rom: _io.BufferedReader, offsetTuples: [(int, int, str)]):
    pointerList = []
    pointerDict = {}
    for tup in offsetTuples:  # Format is (Double Pointer, New Pointer, Symbol)
        offset = tup[0]
        rom.seek(offset)
        pointer = ExtractPointer(rom.read(4))
        pointerList.append(pointer)
        pointerDict[pointer] = (tup[1] + 0x08000000, tup[2])

    offset = 0
    offsetList = []

    while offset < 0xFFFFFD:
        if offset in IGNORED_OFFSETS:
            offset += 4
            continue

        rom.seek(offset)
        word = ExtractPointer(rom.read(4))
        rom.seek(offset)

        for pointer in pointerList:
            if word == pointer:
                offsetList.append((offset, pointerDict[pointer][1]))
                rom.write(bytes(pointerDict[pointer][0].to_bytes(4, 'little')))
                break

        offset += 4

    return offsetList

def ReplaceBytes(rom: _io.BufferedReader, offset: int, data: str):
    ar = offset
    words = data.split()
    for i in range(0, len(words)):
        rom.seek(ar)
        intByte = int(words[i], 16)
        rom.write(bytes(intByte.to_bytes(1, 'big')))
        ar += 1

def TryProcessFileInclusion(line: str, definesDict: dict) -> bool:
    if line.startswith('#include "'):
        try:
            path = line.split('"')[1].strip()
            with open(path, 'r') as file:
                for line in file:
                    if line.startswith('#define '):
                        try:
                            lineList = line.strip().split()
                            title = lineList[1]

                            if len(lineList) == 2 or lineList[2].startswith('//') or lineList[2].startswith('/*'):
                                define = True
                            else:
                                define = lineList[2]

                            definesDict[title] = define
                        except IndexError:
                            print('Error reading define on line"' + line.strip() + '" in file "' + path + '".')

        except Exception as e:
            print('Error including file on line "' + line.strip() + '".')
            print(e)

        return True  # Inclusion line; don't read otherwise

    return False

def TryProcessConditionalCompilation(line: str, definesDict: dict, conditionals: [(str, bool)]) -> bool:
    line = line.strip()
    upperLine = line.upper()
    numWordsOnLine = len(line.split())

    if upperLine.startswith('#IFDEF ') and numWordsOnLine > 1:
        condition = line.strip().split()[1]
        conditionals.insert(0, (condition, True))  # Insert at front
        return True
    elif upperLine.startswith('#IFNDEF ') and numWordsOnLine > 1:
        condition = line.strip().split()[1]
        conditionals.insert(0, (condition, False))  # Insert at front
        return True
    elif upperLine == '#ELSE':
        if len(conditionals) >= 1:  # At least one statement was pushed before
            condition = conditionals.pop(0)
            if condition[1] is True:
                conditionals.insert(0, (condition[0], False))  # Invert old statement
            else:
                conditionals.insert(0, (condition[0], True))  # Invert old statement
            return True
    elif upperLine == '#ENDIF':
        conditionals.pop(0)  # Remove first element (last pushed)
        return True
    else:
        for condition in conditionals:
            definedType = condition[1]
            condition = condition[0]

            if definedType is True:  # From #ifdef
                if condition not in definesDict:
                    return True  # If something isn't defined then skip the line
            else:  # From #ifndef
                if condition in definesDict:
                    return True  # If something is defined then skip the line

    return False

def ExpandROMTo32MB(rom_path):
    TARGET_SIZE = 32 * 1024 * 1024  # 32MB
    if not os.path.exists(rom_path):
        print(f"[!] ROM '{rom_path}' not found for expansion.")
        return
    with open(rom_path, "rb+") as f:
        f.seek(0, os.SEEK_END)
        current_size = f.tell()
        if current_size >= TARGET_SIZE:
            print(f"[✓] ROM is already {current_size // (1024 * 1024)}MB! Good.")
            return
        padding = TARGET_SIZE - current_size
        f.write(b'\xFF' * padding)
        print(f"ROM expanded from {current_size // (1024 * 1024)}MB to 32MB.")

def main():
    startTime = datetime.now()

    try:
        shutil.copyfile(SOURCE_ROM, ROM_NAME)
        ExpandROMTo32MB(ROM_NAME)
    except FileNotFoundError:
        print('Error: Insertion could not be completed.\n'
              + 'Could not find source rom: "' + SOURCE_ROM + '".\n'
              + 'Please make sure a rom with this name exists in the root.')
        sys.exit(1)
    except PermissionError:
        print('Error: Insertion could not be completed.\n'
              + '"' + ROM_NAME + '" is currently in use by another application.'
              + '\nPlease free it up before trying again.')
        sys.exit(1)

    with open(ROM_NAME, 'rb+') as rom:
        print("Inserting code.")
        table = GetSymbols(GetTextSection())
        rom.seek(OFFSET_TO_PUT)
        with open(OUTPUT, 'rb') as binary:
            rom.write(binary.read())
            binary.close()

        # Adjust symbol table
        for entry in table:
            table[entry] += OFFSET_TO_PUT

        # Insert byte changes
        if os.path.isfile(BYTE_REPLACEMENT):
            with open(BYTE_REPLACEMENT, 'r') as replacelist:
                definesDict = {}
                conditionals = []
                for line in replacelist:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    offset = int(line[:8], 16) - 0x08000000
                    try:
                        ReplaceBytes(rom, offset, line[9:].strip())
                    except ValueError: #Try loading from the defines dict if unrecognizable character
                        newNumber = definesDict[line[9:].strip()]
                        try:
                            newNumber = int(newNumber)
                        except ValueError:
                            newNumber = int(newNumber, 16)

                        newNumber = str(hex(newNumber)).split('0x')[1]
                        ReplaceBytes(rom, offset, newNumber)

        # Do Special Inserts
        if os.path.isfile(SPECIAL_INSERTS) and os.path.isfile(SPECIAL_INSERTS_OUT):
            with open(SPECIAL_INSERTS, 'r') as file:
                offsetList = []
                for line in file:
                    if line.strip().startswith('.org '):
                        offsetList.append(int(line.split('.org ')[1].split(',')[0], 16))

                offsetList.sort()

            with open(SPECIAL_INSERTS_OUT, 'rb') as binFile:
                for offset in offsetList:
                    originalOffset = offset
                    dataList = ""

                    if offsetList.index(offset) == len(offsetList) - 1:
                        while True:
                            try:
                                binFile.seek(offset)
                                dataList += hex(binFile.read(1)[0]) + ' '
                            except IndexError:
                                break

                            offset += 1
                    else:
                        binFile.seek(offset)
                        word = ExtractPointer(binFile.read(4))

                        while word != 0xFFFFFFFF:
                            binFile.seek(offset)
                            dataList += hex(binFile.read(1)[0]) + ' '
                            offset += 1

                            if offset in offsetList:  # Overlapping data
                                break

                            word = ExtractPointer(binFile.read(4))

                    ReplaceBytes(rom, originalOffset, dataList.strip())

        # Read hooks from a file
        if os.path.isfile(HOOKS):
            with open(HOOKS, 'r') as hookList:
                definesDict = {}
                conditionals = []
                for line in hookList:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    symbol, address, register = line.split()
                    offset = int(address, 16) - 0x08000000
                    try:
                        code = table[symbol]
                    except KeyError:
                        print('Symbol missing:', symbol)
                        continue

                    Hook(rom, code, offset, int(register))

        # Read repoints from a file
        if os.path.isfile(REPOINTS):
            with open(REPOINTS, 'r') as repointList:
                definesDict = {}
                conditionals = []
                for line in repointList:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    if len(line.split()) == 2:
                        symbol, address = line.split()
                        offset = int(address, 16) - 0x08000000
                        try:
                            code = table[symbol]
                        except KeyError:
                            print('Symbol missing:', symbol)
                            continue

                        Repoint(rom, code, offset)

                    if len(line.split()) == 3:
                        symbol, address, slide = line.split()
                        offset = int(address, 16) - 0x08000000
                        try:
                            code = table[symbol]
                        except KeyError:
                            print('Symbol missing:', symbol)
                            continue

                        Repoint(rom, code, offset, int(slide))

        symbolsRepointed = set()
        if os.path.isfile(GENERATED_REPOINTS):
            with open(GENERATED_REPOINTS, 'r') as repointList:
                for line in repointList:
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    symbol, address = line.split()
                    offset = int(address)
                    try:
                        code = table[symbol]
                    except KeyError:
                        print('Symbol missing:', symbol)
                        continue

                    symbolsRepointed.add(symbol)
                    Repoint(rom, code, offset)

        else:
            with open(GENERATED_REPOINTS, 'w') as repointList:
                repointList.write('##This is a generated file at runtime. Do not modify it!\n')

        if os.path.isfile(REPOINT_ALL):
            offsetsToRepointTogether = []
            with open(REPOINT_ALL, 'r') as repointList:
                definesDict = {}
                conditionals = []
                for line in repointList:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    symbol, address = line.split()
                    offset = int(address, 16) - 0x08000000

                    if symbol in symbolsRepointed:
                        continue

                    try:
                        code = table[symbol]
                    except KeyError:
                        print('Symbol missing:', symbol)
                        continue
                    offsetsToRepointTogether.append((offset, code, symbol))

                if offsetsToRepointTogether != []:
                    offsets = RealRepoint(rom, offsetsToRepointTogether) # Format is [(offset, symbol), ...]

                    output = open(GENERATED_REPOINTS, 'a')
                    for tup in offsets:
                        output.write(tup[1] + ' ' + str(tup[0]) + '\n')
                    output.close()

        # Read routine repoints from a file
        if os.path.isfile(ROUTINE_POINTERS):
            with open(ROUTINE_POINTERS, 'r') as pointerlist:
                definesDict = {}
                conditionals = []
                for line in pointerlist:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    symbol, address = line.split()
                    offset = int(address, 16) - 0x08000000
                    try:
                        code = table[symbol]
                    except KeyError:
                        print('Symbol missing:', symbol)
                        continue

                    Repoint(rom, code, offset, 1)

        # Read routine rewrite wrapper from a file
        if os.path.isfile(FUNCTION_REWRITES):
            with open(FUNCTION_REWRITES, 'r') as frwlist:
                definesDict = {}
                conditionals = []
                for line in frwlist:
                    if TryProcessFileInclusion(line, definesDict):
                        continue
                    if TryProcessConditionalCompilation(line, definesDict, conditionals):
                        continue
                    if line.strip().startswith('#') or line.strip() == '':
                        continue

                    symbol, address, numParams, isReturning = line.split()
                    offset = int(address, 16) - 0x08000000
                    try:
                        code = table[symbol]
                    except KeyError:
                        print('Symbol missing:', symbol)
                        continue

                    FunctionWrap(rom, code, offset, int(numParams), int(isReturning))

        width = max(map(len, table.keys())) + 1
        if os.path.isfile('offsets.ini'):
            offsetIni = open('offsets.ini', 'r+')
        else:
            offsetIni = open('offsets.ini', 'w')

        offsetIni.truncate()
        for key in sorted(table.keys()):
            fstr = ('{:' + str(width) + '} {:08X}')
            offsetIni.write(fstr.format(key + ':', table[key] + 0x08000000) + '\n')
        offsetIni.close()

        print('Inserted in ' + str(datetime.now() - startTime) + '.')

        # Aplicar randomização APÓS gerar test.gba
        ApplyWildEncounterRandomization()

def ApplyWildEncounterRandomization():
    """Aplica randomização de encontros selvagens na ROM final"""
    print("Checking wild encounter randomization...")

    # Verificar se as configurações estão habilitadas
    config_file = "include/wild_encounters_config.h"
    if not os.path.exists(config_file):
        print("⚠️  Configuration file not found, skipping randomization")
        return

    with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    if "RANDOMIZER_ENABLED" not in content or "TRUE" not in content:
        print("⚠️  Randomization is disabled in configuration")
        return

    print("✅ Randomization is ENABLED in configuration")
    print("✅ Applying randomization to test.gba...")

    # GARANTIR LEITURA DA ROM ORIGINAL PARA AMBOS RANDOMIZERS
    if not os.path.exists(SOURCE_ROM):
        print(f"❌ Original ROM '{SOURCE_ROM}' not found for data reading")
        return

    # RANDOMIZAÇÃO REAL - Modificar dados da ROM
    if ApplyRealRandomization():
        print("✅ Environment filters applied")
        print("✅ Similar Strength algorithm applied")
        print("✅ 1440+ Pokemon support active")
        print("✅ Wild encounter randomization completed!")
    else:
        print("⚠️  Randomization failed, ROM unchanged")

    # RANDOMIZAÇÃO DE TREINADORES
    ApplyTrainerRandomization()

def ApplyRealRandomization():
    """Aplica randomização real seguindo a lógica exata do randomizer Java"""
    import random
    import struct

    if not os.path.exists("test.gba"):
        print("❌ test.gba not found")
        return False

    try:
        with open("test.gba", "r+b") as rom:
            # 1. CARREGAR DADOS DE POKÉMON DA ROM (como o Java faz)
            pokemon_stats_offset = FindPokemonStatsOffset(rom)
            if pokemon_stats_offset == 0:
                print("❌ Pokemon stats table not found")
                return False

            print(f"✅ Pokemon stats found at 0x{pokemon_stats_offset:08X}")

            # 2. ENCONTRAR TABELA DE ENCONTROS SELVAGENS (como o Java faz)
            wild_table_offset = FindWildPokemonTable(rom)
            if wild_table_offset == 0:
                print("❌ Wild Pokemon table not found")
                return False

            print(f"✅ Wild Pokemon table found at 0x{wild_table_offset:08X}")

            # 3. PROCESSAR CADA ENTRADA DA TABELA COM VALIDAÇÃO RIGOROSA
            modifications = 0
            total_slots_processed = 0
            offset = wild_table_offset
            entries_processed = 0
            seen_offsets = set()  # Evitar duplicatas como o Java faz

            # Contadores por tipo de área
            grass_areas = 0
            water_areas = 0
            fish_areas = 0
            grass_slots_processed = 0
            water_slots_processed = 0
            fish_slots_processed = 0
            grass_slots_modified = 0
            water_slots_modified = 0
            fish_slots_modified = 0

            while True:
                rom.seek(offset)
                entry_data = rom.read(20)  # 20 bytes por entrada

                if len(entry_data) < 20:
                    break

                bank = entry_data[0]
                map_id = entry_data[1]

                # Fim da tabela
                if bank == 0xFF and map_id == 0xFF:
                    break

                # Ler ponteiros para cada tipo de encontro
                grass_ptr = struct.unpack('<I', entry_data[4:8])[0]
                water_ptr = struct.unpack('<I', entry_data[8:12])[0]
                tree_ptr = struct.unpack('<I', entry_data[12:16])[0]
                fish_ptr = struct.unpack('<I', entry_data[16:20])[0]

                # Debug: mostrar informações da entrada
                if entries_processed < 3:  # Mostrar apenas as primeiras 3
                    print(f"  Entry {entries_processed}: Bank={bank}, Map={map_id}")
                    print(f"    Grass=0x{grass_ptr:08X}, Water=0x{water_ptr:08X}, Fish=0x{fish_ptr:08X}")

                # VALIDAÇÃO RIGOROSA: Randomizar apenas se for área válida e não duplicada
                if IsValidWildEncounterPointer(rom, grass_ptr) and grass_ptr not in seen_offsets:
                    grass_offset = grass_ptr - 0x08000000
                    data_ptr = GetEncounterDataPointer(rom, grass_offset)
                    if data_ptr not in seen_offsets:
                        grass_areas += 1
                        grass_slots_processed += 12
                        total_slots_processed += 12
                        mods = RandomizeEncounterAreaSafe(rom, grass_offset, 12, "Grass", pokemon_stats_offset)
                        modifications += mods
                        grass_slots_modified += mods
                        seen_offsets.add(data_ptr)
                        if entries_processed < 3 and mods > 0:
                            print(f"    Grass: {mods} modifications")

                if IsValidWildEncounterPointer(rom, water_ptr) and water_ptr not in seen_offsets:
                    water_offset = water_ptr - 0x08000000
                    data_ptr = GetEncounterDataPointer(rom, water_offset)
                    if data_ptr not in seen_offsets:
                        water_areas += 1
                        water_slots_processed += 5
                        total_slots_processed += 5
                        mods = RandomizeEncounterAreaSafe(rom, water_offset, 5, "Water", pokemon_stats_offset)
                        modifications += mods
                        water_slots_modified += mods
                        seen_offsets.add(data_ptr)
                        if entries_processed < 3 and mods > 0:
                            print(f"    Water: {mods} modifications")

                if IsValidWildEncounterPointer(rom, fish_ptr) and fish_ptr not in seen_offsets:
                    fish_offset = fish_ptr - 0x08000000
                    data_ptr = GetEncounterDataPointer(rom, fish_offset)
                    if data_ptr not in seen_offsets:
                        fish_areas += 1
                        fish_slots_processed += 10
                        total_slots_processed += 10
                        mods = RandomizeEncounterAreaSafe(rom, fish_offset, 10, "Fish", pokemon_stats_offset)
                        modifications += mods
                        fish_slots_modified += mods
                        seen_offsets.add(data_ptr)
                        if entries_processed < 3 and mods > 0:
                            print(f"    Fish: {mods} modifications")

                offset += 20  # Próxima entrada
                entries_processed += 1

                # Proteção contra loop infinito
                if offset > wild_table_offset + 0x10000 or entries_processed > 1000:
                    break

            # Estatísticas detalhadas
            print(f"  Processed {entries_processed} table entries")
            print(f"📊 ESTATÍSTICAS DETALHADAS:")
            print(f"   🌱 Grass: {grass_areas} áreas, {grass_slots_processed} slots, {grass_slots_modified} modificados ({(grass_slots_modified/grass_slots_processed*100) if grass_slots_processed > 0 else 0:.1f}%)")
            print(f"   🌊 Water: {water_areas} áreas, {water_slots_processed} slots, {water_slots_modified} modificados ({(water_slots_modified/water_slots_processed*100) if water_slots_processed > 0 else 0:.1f}%)")
            print(f"   🎣 Fish: {fish_areas} áreas, {fish_slots_processed} slots, {fish_slots_modified} modificados ({(fish_slots_modified/fish_slots_processed*100) if fish_slots_processed > 0 else 0:.1f}%)")
            print(f"📊 {total_slots_processed} slots totais processados")
            print(f"✅ {modifications} Pokémon randomizados ({(modifications/total_slots_processed*100) if total_slots_processed > 0 else 0:.1f}% do total)")
            return modifications > 0

    except Exception as e:
        print(f"❌ Erro na randomização: {e}")
        return False

def FindPokemonStatsOffset(rom):
    """Encontra offset da tabela de stats de Pokémon (como o Java faz)"""
    # Para FireRed, o offset é conhecido
    # Baseado no config gen3_offsets.ini: PokemonStats=0x254784
    return 0x254784

def FindWildPokemonTable(rom):
    """Encontra a tabela de encontros selvagens usando o mesmo padrão do Java"""
    import struct

    # Padrão correto do randomizer Java: "0348048009E00000FFFF0000"
    pattern = bytes([0x03, 0x48, 0x04, 0x80, 0x09, 0xE0, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00])

    rom.seek(0)
    rom_data = rom.read()

    pos = rom_data.find(pattern)
    if pos != -1:
        # O ponteiro está 12 bytes após o padrão (como no Java)
        rom.seek(pos + 12)
        pointer_data = rom.read(4)
        if len(pointer_data) == 4:
            pointer = struct.unpack('<I', pointer_data)[0]
            # Converter de ponteiro ROM para offset de arquivo
            if 0x08000000 <= pointer < 0x09000000:
                file_offset = pointer - 0x08000000
                print(f"  Found pattern at 0x{pos:08X}, pointer 0x{pointer:08X} -> file offset 0x{file_offset:08X}")
                return file_offset

    # Se não encontrou, tentar busca manual
    print("  Pattern not found, trying manual search...")
    return FindWildTableManually(rom)

def IsValidWildEncounterPointer(rom, ptr):
    """Valida se é um ponteiro válido para encontros selvagens (como o Java faz)"""
    import struct

    if not IsValidPointer(ptr):
        return False

    try:
        # Converter para offset de arquivo
        offset = ptr - 0x08000000

        # Verificar se está dentro dos limites da ROM
        rom.seek(0, 2)  # Ir para o final
        rom_size = rom.tell()

        if offset < 0 or offset >= rom_size - 8:
            return False

        # Ler header da área (rate + ponteiro para dados)
        rom.seek(offset)
        header = rom.read(8)

        if len(header) < 8:
            return False

        rate = header[0]
        data_ptr = struct.unpack('<I', header[4:8])[0]

        # Rate deve ser entre 0-255 (0 = sem encontros)
        if rate > 255:
            return False

        # Se rate é 0, não há dados
        if rate == 0:
            return data_ptr == 0

        # Ponteiro de dados deve ser válido
        return IsValidPointer(data_ptr)

    except:
        return False

def GetEncounterDataPointer(rom, area_offset):
    """Obtém ponteiro para dados de encontros de uma área"""
    import struct

    try:
        rom.seek(area_offset + 4)  # Pular rate (1 byte) + padding (3 bytes)
        pointer_data = rom.read(4)
        if len(pointer_data) == 4:
            return struct.unpack('<I', pointer_data)[0]
    except:
        pass

    return 0

def FindWildTableManually(rom):
    """Busca manual pela tabela de encontros selvagens"""
    import struct

    rom.seek(0)
    rom_data = rom.read()

    # Procurar por sequências que parecem entradas de wild encounters
    for i in range(0, len(rom_data) - 20, 4):
        try:
            # Ler como possível entrada
            bank = rom_data[i]
            map_id = rom_data[i + 1]

            # Verificar se parece uma entrada válida
            if 0 <= bank <= 50 and 0 <= map_id <= 100:
                # Ler ponteiros
                grass_ptr = struct.unpack('<I', rom_data[i+4:i+8])[0]
                water_ptr = struct.unpack('<I', rom_data[i+8:i+12])[0]

                # Se pelo menos um ponteiro é válido
                valid_ptrs = 0
                if 0x08000000 <= grass_ptr < 0x09000000:
                    valid_ptrs += 1
                if 0x08000000 <= water_ptr < 0x09000000:
                    valid_ptrs += 1

                if valid_ptrs >= 1:
                    # Verificar se há mais entradas válidas após esta
                    consecutive_valid = 0
                    for j in range(1, 5):  # Verificar próximas 4 entradas
                        next_offset = i + (j * 20)
                        if next_offset + 20 < len(rom_data):
                            next_bank = rom_data[next_offset]
                            next_map = rom_data[next_offset + 1]

                            if (next_bank == 0xFF and next_map == 0xFF) or (0 <= next_bank <= 50 and 0 <= next_map <= 100):
                                consecutive_valid += 1
                            else:
                                break

                    if consecutive_valid >= 2:  # Pelo menos 3 entradas válidas consecutivas
                        print(f"  Manual search found table at 0x{i:08X}")
                        return i
        except:
            continue

    return 0

def IsValidPointer(ptr):
    """Verifica se é um ponteiro válido para a ROM"""
    return ptr != 0 and 0x08000000 <= ptr < 0x09000000

def RandomizeEncounterAreaSafe(rom, area_offset, num_slots, area_type, pokemon_stats_offset):
    """Randomiza uma área de encontros com validação rigorosa (como o Java faz)"""
    import random
    import struct

    try:
        # Ler header da área
        rom.seek(area_offset)
        header = rom.read(8)
        if len(header) < 8:
            return 0

        rate = header[0]
        data_ptr = struct.unpack('<I', header[4:8])[0]

        # Validações rigorosas
        if rate == 0 or not IsValidPointer(data_ptr):
            return 0

        # Converter ponteiro para offset de arquivo
        data_offset = data_ptr - 0x08000000

        # Verificar se offset é válido
        rom.seek(0, 2)
        rom_size = rom.tell()
        if data_offset < 0 or data_offset >= rom_size - (num_slots * 4):
            return 0

        # Ler dados dos encontros
        rom.seek(data_offset)
        encounter_data = bytearray(rom.read(num_slots * 4))

        if len(encounter_data) < num_slots * 4:
            return 0

        modifications = 0

        # IMPLEMENTAR SUBSTITUIÇÃO 1-PARA-1 (como no randomizer Java)
        # Primeiro, identificar espécies únicas
        unique_species = {}
        for i in range(num_slots):
            slot_offset = i * 4
            species_id = struct.unpack('<H', encounter_data[slot_offset + 2:slot_offset + 4])[0]

            if 1 <= species_id <= 1440:
                if species_id not in unique_species:
                    unique_species[species_id] = []
                unique_species[species_id].append(i)

        # Criar mapeamento de substituições
        species_mapping = {}
        for original_species in unique_species.keys():
            if IsValidPokemonSpecies(rom, original_species, pokemon_stats_offset):
                new_species = ApplySimilarStrengthRandomizationReal(
                    rom, original_species, area_type, pokemon_stats_offset)
                species_mapping[original_species] = new_species

        # Aplicar substituições a todos os slots
        for i in range(num_slots):
            slot_offset = i * 4

            min_level = encounter_data[slot_offset]
            max_level = encounter_data[slot_offset + 1]
            species_id = struct.unpack('<H', encounter_data[slot_offset + 2:slot_offset + 4])[0]

            # VALIDAÇÃO RIGOROSA: Apenas IDs de Pokémon válidos
            if (1 <= species_id <= 1440 and min_level <= max_level and max_level <= 100
                and species_id in species_mapping):

                new_species = species_mapping[species_id]

                if new_species != species_id and 1 <= new_species <= 1440:
                    # Escrever novo species ID
                    struct.pack_into('<H', encounter_data, slot_offset + 2, new_species)
                    modifications += 1

        # Escrever dados modificados de volta
        if modifications > 0:
            rom.seek(data_offset)
            rom.write(encounter_data)

        return modifications

    except Exception as e:
        print(f"❌ Erro ao randomizar área {area_type}: {e}")
        return 0

def RandomizeEncounterArea(rom, area_offset, num_slots, area_type):
    """Função antiga mantida para compatibilidade"""
    return RandomizeEncounterAreaSafe(rom, area_offset, num_slots, area_type, 0x254784)

def ApplySimilarStrengthRandomizationReal(rom, original_species, area_type, pokemon_stats_offset):
    """Aplica randomização Similar Strength com Same Type Priority (1-1 filter aprimorado)"""
    import random

    # VALIDAÇÃO RIGOROSA: Verificar se original é válido
    if not IsValidPokemonSpeciesExpanded(rom, original_species, pokemon_stats_offset):
        return original_species

    # Calcular BST do Pokémon original lendo da ROM
    original_bst = ReadPokemonBSTFromROM(rom, original_species, pokemon_stats_offset)
    if original_bst == 0 or original_bst > 1000:  # BST inválido (expandido para lendários)
        return original_species

    # LER TIPOS DO POKÉMON ORIGINAL
    original_type1, original_type2 = ReadPokemonTypesFromROM(rom, original_species, pokemon_stats_offset)
    if original_type1 == 0:  # Tipo inválido
        return original_species

    # Range de ±10% (como o randomizer Java)
    tolerance = 10
    min_bst = original_bst - (original_bst * tolerance // 100)
    max_bst = original_bst + (original_bst * tolerance // 100)

    # HIERARQUIA REFINADA DE SAME TYPE PRIORITY
    exact_dual_type_candidates = []      # 🥇 Ambos os tipos exatos (Fire/Flying → Fire/Flying)
    exact_single_type_candidates = []    # 🥈 Um tipo exato + outro qualquer (Fire/Flying → Fire/Water)
    partial_type_candidates = []         # 🥉 Pelo menos um tipo em comum (Fire/Flying → Water/Flying)
    other_candidates = []                # 🏅 Apenas filtros ambientais

    # USAR TODOS OS POKÉMON DO PROJETO
    max_species = GetMaxValidSpeciesID(rom, pokemon_stats_offset)

    for species in range(1, min(max_species + 1, 1441)):
        if species == original_species:
            continue

        # VALIDAÇÃO EXPANDIDA: Usar dados do projeto quando possível
        if not IsValidPokemonSpeciesExpanded(rom, species, pokemon_stats_offset):
            continue

        # Verificar bloqueio de Mega/Gigantamax
        if ShouldBlockMegaGigantamax() and IsSpecialForm(species):
            continue

        # Verificar BST real da ROM
        candidate_bst = ReadPokemonBSTFromROM(rom, species, pokemon_stats_offset)
        if candidate_bst == 0 or candidate_bst > 1000:  # BST inválido (expandido)
            continue

        if min_bst <= candidate_bst <= max_bst:
            # Verificar filtros ambientais usando dados reais
            if IsSpeciesValidForAreaReal(rom, species, area_type, pokemon_stats_offset):
                # LER TIPOS DO CANDIDATO
                candidate_type1, candidate_type2 = ReadPokemonTypesFromROM(rom, species, pokemon_stats_offset)

                # CLASSIFICAÇÃO HIERÁRQUICA REFINADA DE TIPOS
                # Prioridade 1: EXACT DUAL TYPE - Ambos os tipos exatos (em qualquer ordem)
                exact_dual_match = ((candidate_type1 == original_type1 and candidate_type2 == original_type2) or
                                   (candidate_type1 == original_type2 and candidate_type2 == original_type1))

                if exact_dual_match:
                    exact_dual_type_candidates.append(species)
                else:
                    # Prioridade 2: EXACT SINGLE TYPE - Pelo menos um tipo exato
                    has_exact_type1 = (candidate_type1 == original_type1 or candidate_type2 == original_type1)
                    has_exact_type2 = (candidate_type1 == original_type2 or candidate_type2 == original_type2)

                    if has_exact_type1 or has_exact_type2:
                        exact_single_type_candidates.append(species)
                    else:
                        # Prioridade 3: PARTIAL TYPE - Qualquer tipo em comum (para casos especiais)
                        # Esta categoria é mantida para compatibilidade, mas raramente será usada
                        # já que exact_single_type já cobre casos de um tipo em comum
                        partial_type_candidates.append(species)

            else:
                # Prioridade 4: ENVIRONMENT ONLY - Apenas filtros ambientais, sem match de tipo
                other_candidates.append(species)

    # SELEÇÃO HIERÁRQUICA REFINADA: Priorizar dual type, depois single type, depois partial, depois environment

    # PRIORIDADE 1: EXACT DUAL TYPE - Ambos os tipos exatos
    if exact_dual_type_candidates:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in exact_dual_type_candidates if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected
    # PRIORIDADE 2: EXACT SINGLE TYPE - Pelo menos um tipo exato
    if exact_single_type_candidates:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in exact_single_type_candidates if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # PRIORIDADE 3: PARTIAL TYPE - Qualquer tipo em comum (raramente usado)
    if partial_type_candidates:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in partial_type_candidates if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # PRIORIDADE 4: ENVIRONMENT ONLY - Apenas filtros ambientais
    if other_candidates:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in other_candidates if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # BUSCA EXPANDIDA: Se não encontrou candidatos suficientes, expandir BST e remover filtros ambientais
    tolerance = 20  # Expandir para ±20%
    min_bst = original_bst - (original_bst * tolerance // 100)
    max_bst = original_bst + (original_bst * tolerance // 100)

    expanded_exact_dual_type = []
    expanded_exact_single_type = []
    expanded_partial_type = []
    expanded_other = []

    for species in range(1, min(max_species + 1, 1441)):
        if species == original_species:
            continue

        if not IsValidPokemonSpeciesExpanded(rom, species, pokemon_stats_offset):
            continue

        if IsSpecialForm(species):
            continue

        candidate_bst = ReadPokemonBSTFromROM(rom, species, pokemon_stats_offset)
        if candidate_bst == 0 or candidate_bst > 1000:
            continue

        if min_bst <= candidate_bst <= max_bst:
            # SEM filtros ambientais na busca expandida
            candidate_type1, candidate_type2 = ReadPokemonTypesFromROM(rom, species, pokemon_stats_offset)

            # CLASSIFICAÇÃO HIERÁRQUICA REFINADA DE TIPOS (BUSCA EXPANDIDA)
            # Prioridade 1: EXACT DUAL TYPE - Ambos os tipos exatos
            exact_dual_match = ((candidate_type1 == original_type1 and candidate_type2 == original_type2) or
                               (candidate_type1 == original_type2 and candidate_type2 == original_type1))

            if exact_dual_match:
                expanded_exact_dual_type.append(species)
            else:
                # Prioridade 2: EXACT SINGLE TYPE - Pelo menos um tipo exato
                has_exact_type1 = (candidate_type1 == original_type1 or candidate_type2 == original_type1)
                has_exact_type2 = (candidate_type1 == original_type2 or candidate_type2 == original_type2)

                if has_exact_type1 or has_exact_type2:
                    expanded_exact_single_type.append(species)
                else:
                    # Prioridade 3: PARTIAL TYPE - Qualquer tipo em comum (raramente usado)
                    expanded_partial_type.append(species)

    # SELEÇÃO HIERÁRQUICA DA BUSCA EXPANDIDA
    print(f"    🎯 1-1 Expanded: Dual: {len(expanded_exact_dual_type)}, Single: {len(expanded_exact_single_type)}, Partial: {len(expanded_partial_type)}, Others: {len(expanded_other)}")

    # PRIORIDADE 1: EXACT DUAL TYPE expandido
    if expanded_exact_dual_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in expanded_exact_dual_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # PRIORIDADE 2: EXACT SINGLE TYPE expandido
    if expanded_exact_single_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in expanded_exact_single_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # PRIORIDADE 3: PARTIAL TYPE expandido
    if expanded_partial_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in expanded_partial_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # PRIORIDADE 4: BST match apenas (sem tipos)
    if expanded_other:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in expanded_other if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # FALLBACK FINAL: Pokémon básicos conhecidos com prioridade de tipo refinada
    basic_pokemon = [1, 4, 7, 10, 13, 16, 19, 21, 23, 25, 27, 29, 32, 35, 37, 39, 41, 43, 46, 48]
    basic_exact_dual_type = []
    basic_exact_single_type = []
    basic_partial_type = []
    basic_other = []

    for species in basic_pokemon:
        if species != original_species and IsValidPokemonSpeciesExpanded(rom, species, pokemon_stats_offset):
            candidate_type1, candidate_type2 = ReadPokemonTypesFromROM(rom, species, pokemon_stats_offset)

            # CLASSIFICAÇÃO HIERÁRQUICA REFINADA DE TIPOS (FALLBACK)
            # Prioridade 1: EXACT DUAL TYPE
            exact_dual_match = ((candidate_type1 == original_type1 and candidate_type2 == original_type2) or
                               (candidate_type1 == original_type2 and candidate_type2 == original_type1))

            if exact_dual_match:
                basic_exact_dual_type.append(species)
            else:
                # Prioridade 2: EXACT SINGLE TYPE
                has_exact_type1 = (candidate_type1 == original_type1 or candidate_type2 == original_type1)
                has_exact_type2 = (candidate_type1 == original_type2 or candidate_type2 == original_type2)

                if has_exact_type1 or has_exact_type2:
                    basic_exact_single_type.append(species)
                else:
                    # Prioridade 3: PARTIAL TYPE (raramente usado)
                    basic_partial_type.append(species)

    # Seleção hierárquica refinada do fallback
    if basic_exact_dual_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in basic_exact_dual_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    if basic_exact_single_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in basic_exact_single_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    if basic_partial_type:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in basic_partial_type if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    if basic_other:
        # FORÇAR EXCLUSÃO DO ORIGINAL
        filtered_candidates = [c for c in basic_other if c != original_species]
        if filtered_candidates:
            selected = random.choice(filtered_candidates)
            return selected

    # Último recurso: retornar original
    return original_species

def IsValidPokemonSpecies(rom, species_id, pokemon_stats_offset):
    """Verifica se é um ID de Pokémon válido lendo dados da ROM"""
    if species_id < 1 or species_id > 1440:
        return False

    try:
        # Ler dados básicos
        bst = ReadPokemonBSTFromROM(rom, species_id, pokemon_stats_offset)
        primary_type, secondary_type = ReadPokemonTypesFromROM(rom, species_id, pokemon_stats_offset)

        # Validações básicas
        if bst == 0 or bst > 800:  # BST inválido
            return False

        if primary_type > 17 or secondary_type > 17:  # Tipos inválidos
            return False

        if bst < 150:  # BST muito baixo (provavelmente dados corrompidos)
            return False

        return True

    except:
        return False

def GetMaxValidSpeciesID(rom, pokemon_stats_offset):
    """Encontra o maior ID de Pokémon válido na ROM - VERSÃO EXPANDIDA"""
    # USAR DADOS DO PROJETO EM VEZ DE VALIDAÇÃO RIGOROSA DA ROM
    project_pokemon_data = LoadProjectPokemonDatabase()

    if project_pokemon_data:
        # Usar todos os Pokémon do projeto
        max_project_id = max(project_pokemon_data.keys()) if project_pokemon_data else 1440
        return min(max_project_id, 1440)  # Limitar a 1440 por segurança

    # Fallback: método original mais permissivo
    max_valid = 386  # Gen 3 padrão
    consecutive_invalid = 0

    for species in range(387, 1441):
        if IsValidPokemonSpeciesExpanded(rom, species, pokemon_stats_offset):
            max_valid = species
            consecutive_invalid = 0  # Reset contador
        else:
            consecutive_invalid += 1

            # Só parar se encontrou MUITOS inválidos consecutivos (50 em vez de 8)
            if consecutive_invalid >= 50:
                break

    return max_valid

def IsValidPokemonSpeciesExpanded(rom, species_id, pokemon_stats_offset):
    """Versão expandida de IsValidPokemonSpecies que usa dados do projeto"""
    if species_id < 1 or species_id > 1440:
        return False

    # PRIORIDADE 1: Verificar no banco de dados do projeto
    project_pokemon_data = LoadProjectPokemonDatabase()
    if project_pokemon_data and species_id in project_pokemon_data:
        pokemon_data = project_pokemon_data[species_id]
        bst = pokemon_data.get('bst', 0)

        # Validações mais permissivas para dados do projeto
        if bst > 0 and bst <= 1000:  # Permitir BST até 1000 (lendários fortes)
            return True

    # PRIORIDADE 2: Validação da ROM (mais permissiva)
    try:
        bst = ReadPokemonBSTFromROM(rom, species_id, pokemon_stats_offset)
        primary_type, secondary_type = ReadPokemonTypesFromROM(rom, species_id, pokemon_stats_offset)

        # Validações mais permissivas
        if bst == 0 or bst > 1000:  # Permitir BST até 1000
            return False

        if primary_type > 25 or secondary_type > 25:  # Permitir tipos expandidos (Fairy = 23)
            return False

        if bst < 100:  # BST muito baixo
            return False

        return True

    except:
        return False

def IsSpecialForm(species_id):
    """Verifica se é uma forma especial (Mega/Gigantamax) usando IDs REAIS do projeto"""

    # MEGA EVOLUTIONS - IDs reais baseados em include/species.h
    mega_evolutions = {
        0x365,  # SPECIES_VENUSAUR_MEGA
        0x366,  # SPECIES_CHARIZARD_MEGA_X
        0x367,  # SPECIES_CHARIZARD_MEGA_Y
        0x368,  # SPECIES_BLASTOISE_MEGA
        0x369,  # SPECIES_BEEDRILL_MEGA
        0x36A,  # SPECIES_PIDGEOT_MEGA
        0x36B,  # SPECIES_ALAKAZAM_MEGA
        0x36C,  # SPECIES_SLOWBRO_MEGA
        0x36D,  # SPECIES_GENGAR_MEGA
        0x36E,  # SPECIES_KANGASKHAN_MEGA
        0x36F,  # SPECIES_PINSIR_MEGA
        0x370,  # SPECIES_GYARADOS_MEGA
        0x371,  # SPECIES_AERODACTYL_MEGA
        0x372,  # SPECIES_MEWTWO_MEGA_X
        0x373,  # SPECIES_MEWTWO_MEGA_Y
        0x374,  # SPECIES_AMPHAROS_MEGA
        0x375,  # SPECIES_STEELIX_MEGA
        0x376,  # SPECIES_SCIZOR_MEGA
        0x377,  # SPECIES_HERACROSS_MEGA
        0x378,  # SPECIES_HOUNDOOM_MEGA
        0x379,  # SPECIES_TYRANITAR_MEGA
        0x37A,  # SPECIES_SCEPTILE_MEGA
        0x37B,  # SPECIES_BLAZIKEN_MEGA
        0x37C,  # SPECIES_SWAMPERT_MEGA
        0x37D,  # SPECIES_GARDEVOIR_MEGA
        0x37E,  # SPECIES_SABLEYE_MEGA
        0x37F,  # SPECIES_MAWILE_MEGA
        0x380,  # SPECIES_AGGRON_MEGA
        0x381,  # SPECIES_MEDICHAM_MEGA
        0x382,  # SPECIES_MANECTRIC_MEGA
        0x383,  # SPECIES_SHARPEDO_MEGA
        0x384,  # SPECIES_CAMERUPT_MEGA
        0x385,  # SPECIES_ALTARIA_MEGA
        0x386,  # SPECIES_BANETTE_MEGA
        0x387,  # SPECIES_ABSOL_MEGA
        0x388,  # SPECIES_GLALIE_MEGA
        0x389,  # SPECIES_SALAMENCE_MEGA
        0x38A,  # SPECIES_METAGROSS_MEGA
        0x38B,  # SPECIES_LATIAS_MEGA
        0x38C,  # SPECIES_LATIOS_MEGA
        0x38D,  # SPECIES_GROUDON_PRIMAL
        0x38E,  # SPECIES_KYOGRE_PRIMAL
        0x38F,  # SPECIES_RAYQUAZA_MEGA
        0x390,  # SPECIES_LOPUNNY_MEGA
        0x391,  # SPECIES_GARCHOMP_MEGA
        0x392,  # SPECIES_LUCARIO_MEGA
        0x393,  # SPECIES_ABOMASNOW_MEGA
        0x394,  # SPECIES_GALLADE_MEGA
        0x395,  # SPECIES_AUDINO_MEGA
        0x396,  # SPECIES_DIANCIE_MEGA
    }

    # GIGANTAMAX FORMS - IDs reais baseados em include/species.h
    gigantamax_forms = {
        0x4EC,  # SPECIES_VENUSAUR_GIGA
        0x4ED,  # SPECIES_CHARIZARD_GIGA
        0x4EE,  # SPECIES_BLASTOISE_GIGA
        0x4EF,  # SPECIES_BUTTERFREE_GIGA
        0x4F0,  # SPECIES_PIKACHU_GIGA
        0x4F1,  # SPECIES_MEOWTH_GIGA
        0x4F2,  # SPECIES_MACHAMP_GIGA
        0x4F3,  # SPECIES_GENGAR_GIGA
        0x4F4,  # SPECIES_KINGLER_GIGA
        0x4F5,  # SPECIES_LAPRAS_GIGA
        0x4F6,  # SPECIES_EEVEE_GIGA
        0x4F7,  # SPECIES_SNORLAX_GIGA
        0x4F8,  # SPECIES_GARBODOR_GIGA
        0x4F9,  # SPECIES_MELMETAL_GIGA
        0x4FA,  # SPECIES_RILLABOOM_GIGA
        0x4FB,  # SPECIES_CINDERACE_GIGA
        0x4FC,  # SPECIES_INTELEON_GIGA
        0x4FD,  # SPECIES_CORVIKNIGHT_GIGA
        0x4FE,  # SPECIES_ORBEETLE_GIGA
        0x4FF,  # SPECIES_DREDNAW_GIGA
        0x500,  # SPECIES_COALOSSAL_GIGA
        0x501,  # SPECIES_FLAPPLE_GIGA
        0x502,  # SPECIES_APPLETUN_GIGA
        0x503,  # SPECIES_SANDACONDA_GIGA
        0x504,  # SPECIES_TOXTRICITY_GIGA
        0x505,  # SPECIES_TOXTRICITY_LOW_KEY_GIGA
        0x506,  # SPECIES_CENTISKORCH_GIGA
        0x507,  # SPECIES_HATTERENE_GIGA
        0x508,  # SPECIES_GRIMMSNARL_GIGA
        0x509,  # SPECIES_ALCREMIE_GIGA
        0x50A,  # SPECIES_COPPERAJAH_GIGA
        0x50B,  # SPECIES_DURALUDON_GIGA
        0x50C,  # SPECIES_URSHIFU_SINGLE_GIGA
        0x50D,  # SPECIES_URSHIFU_RAPID_GIGA
    }

    return species_id in mega_evolutions or species_id in gigantamax_forms

# Cache global para evitar leituras repetidas do arquivo
_mega_gigantamax_block_cache = None

# STEP 1: Global cache for ALL trainer data from original ROM (BPRE0.gba)
_original_trainer_data_cache = None

def ShouldBlockMegaGigantamax():
    """Verifica se deve bloquear Mega Evoluções e Gigantamax baseado na configuração (com cache)"""
    global _mega_gigantamax_block_cache

    if _mega_gigantamax_block_cache is not None:
        return _mega_gigantamax_block_cache

    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        block_mega = "TRAINER_BLOCK_MEGA_EVOLUTIONS TRUE" in content
        block_gigantamax = "TRAINER_BLOCK_GIGANTAMAX TRUE" in content

        _mega_gigantamax_block_cache = block_mega or block_gigantamax
        return _mega_gigantamax_block_cache
    except:
        _mega_gigantamax_block_cache = True  # Bloquear por padrão se não conseguir ler
        return _mega_gigantamax_block_cache

def ReadAllTrainerDataFromOriginalROM():
    """
    STEP 1: Read trainer party data from ROM offset ONCE to obtain original party composition for ALL trainers
    This implements the critical requirement to read from BPRE0.gba only once and cache all data
    """
    import struct
    global _original_trainer_data_cache

    if _original_trainer_data_cache is not None:
        return _original_trainer_data_cache

    if not os.path.exists(SOURCE_ROM):
        print(f"❌ Original ROM '{SOURCE_ROM}' not found for trainer data reading")
        return None

    try:
        with open(SOURCE_ROM, "rb") as original_rom:
            # Find trainer table in original ROM
            trainer_table_offset = FindTrainerTable(original_rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found in original ROM")
                return None

            # Find Pokemon stats offset for type analysis
            pokemon_stats_offset = FindPokemonStatsOffset(original_rom)
            if pokemon_stats_offset == 0:
                print("❌ Pokemon stats table not found in original ROM")
                return None

            print(f"✅ Reading ALL trainer data from original ROM at 0x{trainer_table_offset:08X}")

            # Read ALL 743 trainers from Fire Red
            trainer_count = 0x2E7  # 743 trainers
            all_trainer_data = {}

            for trainer_id in range(1, trainer_count):
                trainer_offset = trainer_table_offset + (trainer_id * 40)  # 40 bytes per trainer

                # Read trainer header
                original_rom.seek(trainer_offset)
                trainer_data = original_rom.read(40)

                if len(trainer_data) >= 40:
                    trainer_class = trainer_data[1]
                    party_size = trainer_data[32]
                    party_ptr = struct.unpack('<I', trainer_data[36:40])[0]

                    if IsValidPointer(party_ptr) and party_size > 0 and party_size <= 6:
                        party_offset = party_ptr - 0x08000000

                        # Read original party data
                        original_party_data = ReadOriginalTrainerPartyData(original_rom, party_offset, party_size)

                        if original_party_data:
                            # Analyze types using project database
                            party_type_analysis = AnalyzeOriginalPartyTypes(original_party_data)

                            all_trainer_data[trainer_id] = {
                                'trainer_class': trainer_class,
                                'party_size': party_size,
                                'party_offset': party_offset,
                                'original_party': original_party_data,
                                'type_analysis': party_type_analysis,
                                'eligible': IsTrainerEligibleForRandomization(trainer_class, trainer_id)
                            }

            _original_trainer_data_cache = {
                'trainers': all_trainer_data,
                'pokemon_stats_offset': pokemon_stats_offset,
                'total_trainers': len(all_trainer_data),
                'eligible_trainers': sum(1 for t in all_trainer_data.values() if t['eligible'])
            }

            print(f"✅ Cached {len(all_trainer_data)} trainers from original ROM")
            print(f"   {_original_trainer_data_cache['eligible_trainers']} eligible for randomization")

            return _original_trainer_data_cache

    except Exception as e:
        print(f"❌ Error reading trainer data from original ROM: {e}")
        return None

def ReadPokemonBSTFromROM(rom, species_id, pokemon_stats_offset):
    """Lê BST real de um Pokémon da ROM (como o Java faz)"""
    import struct

    if species_id < 1 or species_id > 1440:
        return 0

    try:
        # Cada entrada de stats tem 28 bytes (0x1C)
        entry_size = 0x1C
        offset = pokemon_stats_offset + (species_id * entry_size)

        rom.seek(offset)
        stats_data = rom.read(entry_size)

        if len(stats_data) < 6:
            return 0

        # Ler stats (como definido em Gen3Constants.java)
        hp = stats_data[0]      # bsHPOffset = 0
        attack = stats_data[1]   # bsAttackOffset = 1
        defense = stats_data[2]  # bsDefenseOffset = 2
        speed = stats_data[3]    # bsSpeedOffset = 3
        spatk = stats_data[4]    # bsSpAtkOffset = 4
        spdef = stats_data[5]    # bsSpDefOffset = 5

        # Calcular BST
        bst = hp + attack + defense + speed + spatk + spdef

        # Tratamento especial para Shedinja (como no Java)
        if species_id == 292:  # SPECIES_SHEDINJA
            # Recalcula BST sem HP e multiplica por 6/5 para compensar
            bst = (attack + defense + spatk + spdef + speed) * 6 // 5

        return bst

    except Exception as e:
        return 0

def ReadPokemonTypesFromROM(rom, species_id, pokemon_stats_offset):
    """Lê tipos reais de um Pokémon da ROM (como o Java faz)"""
    if species_id < 1 or species_id > 1440:
        return (0, 0)

    try:
        # Cada entrada de stats tem 28 bytes (0x1C)
        entry_size = 0x1C
        offset = pokemon_stats_offset + (species_id * entry_size)

        rom.seek(offset + 6)  # bsPrimaryTypeOffset = 6
        type_data = rom.read(2)

        if len(type_data) < 2:
            return (0, 0)

        primary_type = type_data[0]    # bsPrimaryTypeOffset = 6
        secondary_type = type_data[1]  # bsSecondaryTypeOffset = 7

        return (primary_type, secondary_type)

    except:
        return (0, 0)

def IsSpeciesValidForAreaReal(rom, species_id, area_type, pokemon_stats_offset):
    """Verifica se o Pokémon é adequado para o tipo de área usando dados reais da ROM"""

    # Verificar se não é forma especial banida
    if IsSpecialForm(species_id):
        return False

    # Ler tipos reais da ROM
    primary_type, secondary_type = ReadPokemonTypesFromROM(rom, species_id, pokemon_stats_offset)

    # Constantes de tipos (baseadas em Gen3Constants.java)
    TYPE_WATER = 0x0B
    TYPE_GRASS = 0x0C
    TYPE_ROCK = 0x05
    TYPE_GROUND = 0x04
    TYPE_STEEL = 0x08
    TYPE_FIGHTING = 0x01
    TYPE_DARK = 0x11
    TYPE_GHOST = 0x07

    if area_type == "Water":
        # Água DEVE ter pelo menos um tipo Water
        return primary_type == TYPE_WATER or secondary_type == TYPE_WATER

    elif area_type == "Fish":
        # Pesca DEVE ter pelo menos um tipo Water
        return primary_type == TYPE_WATER or secondary_type == TYPE_WATER

    elif area_type == "Rock":
        # Rock Smash deve ter tipos apropriados
        valid_types = {TYPE_ROCK, TYPE_GROUND, TYPE_FIGHTING, TYPE_STEEL}
        return primary_type in valid_types or secondary_type in valid_types

    elif area_type == "Cave":
        # Cavernas preferem tipos específicos, mas aceitam outros
        preferred_types = {TYPE_ROCK, TYPE_GROUND, TYPE_DARK, TYPE_GHOST}
        has_preferred = primary_type in preferred_types or secondary_type in preferred_types

        # Se tem tipo preferido, aceitar
        if has_preferred:
            return True

        # Se não tem tipo preferido, evitar Pokémon puramente aquáticos
        if primary_type == TYPE_WATER and secondary_type == TYPE_WATER:
            return False

        return True

    else:  # Grass/Normal
        # Grama aceita qualquer tipo, mas evita Pokémon puramente aquáticos
        if primary_type == TYPE_WATER and secondary_type == TYPE_WATER:
            return False  # Pokémon puramente aquático
        return True

def ApplySimilarStrengthRandomization(original_species, area_type):
    """Função antiga mantida para compatibilidade"""
    import random

    # Implementação simplificada para compatibilidade
    candidates = []
    for species in range(max(1, original_species - 50), min(1441, original_species + 50)):
        if species != original_species:
            candidates.append(species)

    return random.choice(candidates) if candidates else original_species

def GetPokemonBST(species_id):
    """Calcula BST aproximado baseado no ID (simplificado)"""
    # Implementação simplificada - em uma versão completa,
    # isso leria os dados reais de base stats da ROM

    # BST aproximado baseado em padrões conhecidos
    if species_id <= 151:  # Gen 1
        base_bst = 300 + (species_id * 2)
    elif species_id <= 251:  # Gen 2
        base_bst = 320 + ((species_id - 151) * 2)
    elif species_id <= 386:  # Gen 3
        base_bst = 340 + ((species_id - 251) * 2)
    else:  # Gen 4+
        base_bst = 400 + ((species_id - 386) * 1.5)

    # Adicionar variação aleatória para simular diferenças reais
    import random
    variation = random.randint(-50, 50)

    return max(200, min(800, int(base_bst + variation)))

def IsSpeciesValidForArea(species_id, area_type):
    """Verifica se o Pokémon é adequado para o tipo de área"""
    # Implementação simplificada de filtros ambientais

    if area_type == "Water":
        # Água deve ter tipos Water (simplificado)
        # IDs conhecidos de Pokémon Water-type
        water_pokemon = {7, 8, 9, 54, 55, 60, 61, 62, 72, 73, 79, 80, 86, 87, 90, 91, 98, 99,
                        116, 117, 118, 119, 120, 121, 129, 130, 131, 134, 138, 139, 140, 141}

        # Se é um Pokémon conhecido Water-type, permitir
        if species_id in water_pokemon:
            return True

        # Para outros, usar heurística baseada em ID
        # Pokémon com IDs específicos que são tipicamente aquáticos
        return (species_id % 10) in [0, 4, 7, 9]  # 40% de chance

    elif area_type == "Fish":
        # Pesca deve ter tipos Water
        water_pokemon = {7, 8, 9, 54, 55, 60, 61, 62, 72, 73, 79, 80, 86, 87, 90, 91, 98, 99,
                        116, 117, 118, 119, 120, 121, 129, 130, 131, 134, 138, 139, 140, 141}

        if species_id in water_pokemon:
            return True

        return (species_id % 10) in [0, 4, 7, 9]

    else:  # Grass/Cave
        # Grama/caverna aceita qualquer tipo
        return True

def LoadPartyExpansionSettings():
    """Carrega configurações de expansão de equipe do arquivo de configuração"""
    settings = {
        'enabled': False,
        'regular': 0,
        'important': 0,
        'boss': 0,
        'only_additional': False,
        'allow_legendaries_important': False,
        'allow_legendaries_boss': False
    }

    config_file = "include/wild_encounters_config.h"
    if not os.path.exists(config_file):
        return settings

    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Extrair valores das configurações
        import re

        # Número de Pokémon adicionais
        regular_match = re.search(r'#define\s+ADDITIONAL_REGULAR_TRAINER_POKEMON\s+(\d+)', content)
        if regular_match:
            settings['regular'] = int(regular_match.group(1))

        important_match = re.search(r'#define\s+ADDITIONAL_IMPORTANT_TRAINER_POKEMON\s+(\d+)', content)
        if important_match:
            settings['important'] = int(important_match.group(1))

        boss_match = re.search(r'#define\s+ADDITIONAL_BOSS_TRAINER_POKEMON\s+(\d+)', content)
        if boss_match:
            settings['boss'] = int(boss_match.group(1))

        # Modo de randomização
        only_additional_match = re.search(r'#define\s+RANDOMIZE_ONLY_ADDITIONAL_POKEMON\s+(TRUE|FALSE)', content)
        if only_additional_match:
            settings['only_additional'] = only_additional_match.group(1) == 'TRUE'

        # Lendários em slots adicionais
        leg_important_match = re.search(r'#define\s+ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_IMPORTANT\s+(TRUE|FALSE)', content)
        if leg_important_match:
            settings['allow_legendaries_important'] = leg_important_match.group(1) == 'TRUE'

        leg_boss_match = re.search(r'#define\s+ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_BOSS\s+(TRUE|FALSE)', content)
        if leg_boss_match:
            settings['allow_legendaries_boss'] = leg_boss_match.group(1) == 'TRUE'

        # Verificar se está habilitado
        settings['enabled'] = (settings['regular'] > 0 or settings['important'] > 0 or settings['boss'] > 0)

    except Exception as e:
        print(f"⚠️  Error loading party expansion settings: {e}")

    return settings

def ApplyTrainerRandomization():
    """
    CORRECTED WORKFLOW: Aplica randomização de treinadores seguindo a sequência correta

    STEP 1: Unmodified ROM Data Reading (One-time only) BPRE0.gba
    STEP 2: Type Analysis
    STEP 3: Pokemon Selection from Project Database
    STEP 4: Randomization Rules for Additional Pokemon
    STEP 5: Legendary Substitution (if enabled)
    """
    print("Checking trainer randomization...")

    # Verificar se a randomização de treinadores está habilitada
    config_file = "include/wild_encounters_config.h"
    if not os.path.exists(config_file):
        print("⚠️  Configuration file not found")
        return

    with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    if "TRAINER_RANDOMIZATION_ENABLED" not in content or "TRUE" not in content:
        print("⚠️  Trainer randomization is disabled in configuration")
        return

    print("✅ Trainer randomization is ENABLED in configuration")
    print("✅ Applying CORRECTED trainer randomization workflow to test.gba...")

    # STEP 1: Read ALL trainer data from original ROM ONCE
    print("📖 STEP 1: Reading ALL trainer data from original ROM (BPRE0.gba)...")
    all_trainer_data = ReadAllTrainerDataFromOriginalROM()
    if not all_trainer_data:
        print("❌ Failed to read trainer data from original ROM")
        return

    print(f"✅ Successfully read {all_trainer_data.get('total_trainers', 0)} trainers from original ROM")

    # STEP 2: Type Analysis for ALL trainers
    print("🔍 STEP 2: Analyzing predominant types for all trainers...")
    trainer_type_analysis = {}

    # Extract trainers from the cached structure
    cached_trainers = all_trainer_data.get('trainers', {})
    for trainer_id, trainer_data in cached_trainers.items():
        # The original_party is already analyzed and stored in type_analysis
        trainer_type_analysis[trainer_id] = trainer_data.get('type_analysis', {})

    print(f"✅ Type analysis completed for {len(trainer_type_analysis)} trainers")

    # STEP 3: Load Project Pokemon Database ONCE
    print("📊 STEP 3: Loading project Pokemon database...")
    project_pokemon_data = LoadProjectPokemonDatabase()
    print(f"✅ Loaded {len(project_pokemon_data)} Pokemon from project database")

    # STEP 4: Apply randomization workflow
    print("🎲 STEP 4: Applying randomization rules...")

    # Apply the real trainer randomization using the corrected workflow
    if ApplyRealTrainerRandomization():
        print("✅ Trainer categorization applied")
        print("✅ Similar strength matching applied")
        print("✅ Regular trainers randomized")
        print("✅ Trainer randomization completed successfully!")
    else:
        print("❌ Trainer randomization failed, ROM unchanged")

def ApplyRealTrainerRandomization():
    """
    CORRECTED: Applies trainer randomization following the exact 5-step sequence
    STEP 1: Read ALL trainer data from original ROM once and cache it
    """
    import random
    import struct

    if not os.path.exists("test.gba"):
        print("❌ test.gba not found")
        return False

    # STEP 1: Read ALL trainer data from original ROM (BPRE0.gba) ONCE
    original_trainer_cache = ReadAllTrainerDataFromOriginalROM()
    if not original_trainer_cache:
        return False

    try:
        # Open only the modified ROM for writing (original data is cached)
        with open("test.gba", "r+b") as rom:
            # Extract cached data
            cached_trainers = original_trainer_cache['trainers']
            pokemon_stats_offset = original_trainer_cache['pokemon_stats_offset']
            total_trainers = original_trainer_cache['total_trainers']
            eligible_count = original_trainer_cache['eligible_trainers']

            print(f"✅ Using cached data for {total_trainers} trainers ({eligible_count} eligible)")

            # STEP 2: Read expansion settings from configuration (CORRECTED)
            expansion_config = ReadTrainerExpansionSettings()
            expansion_settings = {
                'enabled': True,
                'regular': expansion_config['additional_regular'],
                'important': expansion_config['additional_important'],
                'boss': expansion_config['additional_boss'],
                'randomize_only_additional': expansion_config['randomize_only_additional'],
                'allow_legendaries_important': expansion_config['allow_legendaries_important'],
                'allow_legendaries_boss': expansion_config['allow_legendaries_boss']
            }

            if expansion_settings['enabled']:
                print(f"✅ Party Expansion enabled: Regular +{expansion_settings['regular']}, Important +{expansion_settings['important']}, Boss +{expansion_settings['boss']}")
                print(f"   Randomize only additional: {expansion_settings['randomize_only_additional']}")
                print(f"   Allow legendaries - Important: {expansion_settings['allow_legendaries_important']}, Boss: {expansion_settings['allow_legendaries_boss']}")

            # Process each trainer using cached original data
            # Counters (CORRECTED: Separate counters for clarity)
            pokemon_slots_modified = 0  # Total Pokemon slots modified
            trainers_randomized = 0     # Total trainers that had randomization applied
            expansions = 0              # Total trainers that had party expansion
            trainers_processed = 0      # Total trainers processed
            failed_randomizations = 0   # Total trainers that failed randomization

            # Find trainer table offset in modified ROM for writing
            trainer_table_offset = FindTrainerTable(rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found in modified ROM")
                return False

            for trainer_id, trainer_info in cached_trainers.items():
                trainer_offset = trainer_table_offset + (trainer_id * 40)  # 40 bytes per trainer

                # STEP 2: Party Expansion (if enabled) - using cached original data
                if expansion_settings['enabled']:
                    expansion_result = ExpandTrainerPartyWithCachedData(
                        rom, trainer_offset, trainer_id, trainer_info,
                        expansion_settings, pokemon_stats_offset)
                    if expansion_result:
                        expansions += 1

                # STEP 3-5: Randomization using cached original data
                if trainer_info['eligible']:
                    randomization_result = RandomizeTrainerWithCachedData(
                        rom, trainer_offset, trainer_id, trainer_info, pokemon_stats_offset)

                    # DEBUG: Log result for first few trainers
                    if trainer_id <= 5:
                        print(f"DEBUG: Trainer {trainer_id}, randomization_result: {randomization_result}")

                    if randomization_result > 0:
                        pokemon_slots_modified += randomization_result  # Add actual number of Pokemon slots modified
                        trainers_randomized += 1  # Count this trainer as randomized
                    else:
                        failed_randomizations += 1

                trainers_processed += 1

                # Show progress every 100 trainers
                if trainers_processed % 100 == 0:
                    print(f"  Processed {trainers_processed} trainers...")

            print(f"  Processed {trainers_processed} trainers ({eligible_count} eligible)")
            if expansion_settings['enabled']:
                print(f"✅ {expansions} trainers had their parties expanded")
            print(f"✅ {trainers_randomized} trainers randomized ({pokemon_slots_modified} Pokemon slots modified)")
            if failed_randomizations > 0:
                print(f"⚠️  {failed_randomizations} eligible trainers failed randomization")
            return trainers_randomized > 0 or expansions > 0

    except Exception as e:
        print(f"❌ Erro na randomização de treinadores: {e}")
        return False

def RandomizeTrainerWithCachedData(rom, trainer_offset, trainer_id, trainer_info, pokemon_stats_offset):
    """
    STEP 3-5: Randomization using cached original data following exact sequence
    """
    import struct
    import random

    try:
        # Extract cached original data
        original_party = trainer_info['original_party']
        party_type_analysis = trainer_info['type_analysis']
        trainer_class = trainer_info['trainer_class']
        party_offset = trainer_info['party_offset']

        # Read current party size from modified ROM (may have been expanded)
        rom.seek(trainer_offset + 32)  # Party size at offset 32
        current_party_size = rom.read(1)[0]

        if current_party_size == 0 or current_party_size > 6:
            return False

        # STEP 4: Check RANDOMIZE_ONLY_ADDITIONAL_POKEMON configuration
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()

        if randomize_only_additional:
            # CRITICAL: NEVER modify original party members, only additional slots
            result = RandomizeOnlyAdditionalPokemonWithCache(
                rom, party_offset, current_party_size, original_party,
                party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id)
            return result  # Return actual number of modifications
        else:
            # Randomize all Pokemon in party
            result = RandomizeAllTrainerPokemonWithCache(
                rom, party_offset, current_party_size, original_party,
                party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id)
            return result  # Return actual number of modifications

    except Exception as e:
        return 0

def RandomizeOnlyAdditionalPokemonWithCache(rom, party_offset, current_party_size, original_party, party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id):
    """
    STEP 4: CORRECTED - Process ONLY additional Pokemon slots using cached data
    When RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE:
    - Additional Pokemon were already properly selected during expansion phase
    - This function ONLY fixes invalid levels, does NOT re-randomize species
    - NEVER modify original party members
    """
    import struct
    import random

    try:
        modifications = 0
        original_party_size = len(original_party)

        # Determine how many additional Pokemon slots exist
        additional_slots = current_party_size - original_party_size
        if additional_slots <= 0:
            return 0  # No additional slots to randomize

        # Get predominant types for filtering candidates
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())

        # Collect existing species to prevent duplicates (COMPLETE DUPLICATE PREVENTION)
        existing_species = {pokemon['species'] for pokemon in original_party}

        # Also read current additional Pokemon to prevent duplicates among them
        for i in range(original_party_size, current_party_size):
            pokemon_offset = party_offset + (i * 8)
            rom.seek(pokemon_offset)
            pokemon_data = rom.read(8)
            if len(pokemon_data) >= 8:
                current_species = struct.unpack('<H', pokemon_data[4:6])[0]
                if current_species > 0:
                    existing_species.add(current_species)

        # Gym leaders for logging - CORRECTED: IDs in DECIMAL
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        gym_leader_additions = []

        # DEBUG: Log processing for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Processing trainer {trainer_id}, original_size={original_party_size}, current_size={current_party_size}, additional_slots={additional_slots}")
            print(f"DEBUG: Trainer {trainer_id}, modifications counter starts at: {modifications}")

        # CRITICAL VALIDATION: NEVER modify original party members
        # This function should ONLY process additional slots (i >= original_party_size)
        if current_party_size <= original_party_size:
            # No additional slots to randomize
            if trainer_id <= 5:
                print(f"DEBUG: Trainer {trainer_id}, NO ADDITIONAL SLOTS to randomize (current={current_party_size}, original={original_party_size})")
            return 0

        # CRITICAL FIX: When RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE,
        # additional Pokemon were already properly selected during expansion phase
        # This function should ONLY fix invalid levels, NOT re-randomize species

        # DEBUG: Log boundary information for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Trainer {trainer_id}, BOUNDARY: original slots [0-{original_party_size-1}], additional slots [{original_party_size}-{current_party_size-1}]")
            print(f"DEBUG: Trainer {trainer_id}, RANDOMIZE_ONLY_ADDITIONAL mode - preserving existing additional Pokemon, only fixing levels")

        # PROCESS ONLY ADDITIONAL POKEMON SLOTS (starting from original_party_size)
        # CRITICAL: Do NOT re-randomize species - they were already properly selected
        for i in range(original_party_size, current_party_size):
            pokemon_offset = party_offset + (i * 8)

            # CRITICAL PROTECTION: Double-check we're only processing additional slots
            if i < original_party_size:
                if trainer_id <= 5:
                    print(f"DEBUG: Trainer {trainer_id}, CRITICAL ERROR: Attempted to modify ORIGINAL SLOT {i + 1} (PROTECTION ACTIVE)")
                continue  # Skip original Pokemon slots - NEVER modify them

            # Read current data from ROM
            rom.seek(pokemon_offset)
            pokemon_data = bytearray(rom.read(8))

            if len(pokemon_data) < 8:
                continue

            level = pokemon_data[2]
            current_species = struct.unpack('<H', pokemon_data[4:6])[0]

            # ONLY fix invalid levels - DO NOT change species
            level_changed = False
            if level > 100:
                # Fix invalid level based on original party
                if original_party:
                    effective_level = min(pokemon['level'] for pokemon in original_party)
                else:
                    effective_level = 20  # Fallback
                pokemon_data[2] = effective_level
                level_changed = True
                if trainer_id <= 5:
                    print(f"DEBUG: Trainer {trainer_id}, slot {i + 1}, LEVEL FIXED: {level} -> {effective_level}")
            elif level == 0:
                # Set default level for new slots
                effective_level = 20
                pokemon_data[2] = effective_level
                level_changed = True
                if trainer_id <= 5:
                    print(f"DEBUG: Trainer {trainer_id}, slot {i + 1}, LEVEL SET: 0 -> {effective_level}")

            # Write back ONLY if level was changed - preserve species selection
            if level_changed:
                rom.seek(pokemon_offset)
                rom.write(pokemon_data)
                modifications += 1

            # Log existing Pokemon for gym leaders (do NOT change them)
            if trainer_id in gym_leader_names and current_species > 0:
                gym_leader_additions.append(current_species)

            # DEBUG: Log processing for first few trainers
            if trainer_id <= 5:
                action = "LEVEL_FIXED" if level_changed else "PRESERVED"
                print(f"DEBUG: Trainer {trainer_id}, slot {i + 1} (ADDITIONAL SLOT {i - original_party_size + 1}), species={current_species}, action={action}")

        # STEP 5: Legendary Substitution (if enabled)
        # CRITICAL: When RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE, legendary substitution
        # should be handled during expansion phase, not here
        # This prevents disrupting the carefully selected type-appropriate Pokemon
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()
        if not randomize_only_additional and modifications > 0:  # Only if we added Pokemon AND not in preserve mode
            legendary_substitution_result = ApplyLegendarySubstitution(
                rom, party_offset, current_party_size, original_party_size,
                trainer_class, trainer_id, primary_type, secondary_type, party_types)
            if legendary_substitution_result:
                # CORREÇÃO: SUBSTITUIR um dos Pokémon na lista, não adicionar
                if trainer_id in gym_leader_names and gym_leader_additions:
                    # Substituir o último Pokémon adicionado pelo lendário
                    gym_leader_additions[-1] = legendary_substitution_result

        # Log gym leader results
        if trainer_id in gym_leader_names and gym_leader_additions:
            leader_name = gym_leader_names[trainer_id]
            additions_str = ", ".join([f"#{species}" for species in gym_leader_additions])
            print(f"🏆 {leader_name}: Added {additions_str}")

        # DEBUG: Log final result for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Trainer {trainer_id}, FINAL modifications: {modifications}")

        return modifications

    except Exception as e:
        # DEBUG: Log exception for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Trainer {trainer_id}, EXCEPTION in RandomizeOnlyAdditionalPokemonWithCache: {e}")
        return 0

def SelectPokemonFromProjectDatabase(primary_type, secondary_type, party_types, existing_species, level, trainer_class, trainer_id):
    """
    STEP 3: Pokemon Selection from Project Database following exact hierarchy
    """
    import random

    # Load project Pokemon database
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        # DEBUG: Log database loading failure
        if trainer_id <= 5:  # First few trainers
            print(f"DEBUG: Failed to load Pokemon database for trainer {trainer_id}")
        return None

    # DEBUG: Log database info for first few trainers
    if trainer_id <= 5:
        print(f"DEBUG: Trainer {trainer_id}, database loaded: {len(project_pokemon_data)} Pokemon")

    # Read configuration settings
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        use_similar_strength = "TRAINER_USE_SIMILAR_STRENGTH TRUE" in content
        use_type_themes = "TRAINER_USE_TYPE_THEMES TRUE" in content
        allow_alt_formes = "TRAINER_ALLOW_ALT_FORMES TRUE" in content
        same_type_priority = "TRAINER_SAME_TYPE_PRIORITY TRUE" in content
        force_fully_evolved = "TRAINER_FORCE_FULLY_EVOLVED TRUE" in content

        # Read BST tolerance
        bst_tolerance = 10  # Default ±10%
        import re
        tolerance_match = re.search(r'DEFAULT_BST_TOLERANCE\s+(\d+)', content)
        if tolerance_match:
            bst_tolerance = int(tolerance_match.group(1))

        # Read force evolution level
        force_fully_evolved_level = 40  # Default
        level_match = re.search(r'TRAINER_FORCE_FULLY_EVOLVED_LEVEL\s+(\d+)', content)
        if level_match:
            force_fully_evolved_level = int(level_match.group(1))

    except:
        # Default values if config reading fails
        use_similar_strength = True
        use_type_themes = True
        allow_alt_formes = True
        same_type_priority = True
        force_fully_evolved = False
        bst_tolerance = 10
        force_fully_evolved_level = 40

    # Determine max species based on allow_alt_formes
    max_species = 1440 if allow_alt_formes else 1025

    # STEP 3: Apply established priority hierarchy

    # PRIORITY 1: Same Type Priority (exact types)
    if same_type_priority and (primary_type is not None or secondary_type is not None):
        # DEBUG: Log type priority attempt for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Trainer {trainer_id}, trying same type priority: primary={primary_type}, secondary={secondary_type}")

        # Filter Pokemon by type directly from project database
        type_filtered = []
        for species_id in range(1, max_species + 1):
            if species_id in existing_species:
                continue
            if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
                continue

            # CORREÇÃO CRÍTICA: Bloquear lendários e míticos
            if IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id):
                continue

            pokemon_data = project_pokemon_data.get(species_id)
            if pokemon_data:
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')

                # Check for exact type match
                if (type1 == primary_type or type2 == primary_type or
                    (secondary_type is not None and (type1 == secondary_type or type2 == secondary_type))):
                    type_filtered.append(species_id)

        # DEBUG: Log type filtering results for first few trainers
        if trainer_id <= 5:
            print(f"DEBUG: Trainer {trainer_id}, same type candidates: {len(type_filtered)}")

        if type_filtered:
            selected = random.choice(type_filtered)
            # Apply Force Fully Evolved if configured
            if force_fully_evolved and level >= force_fully_evolved_level:
                selected = FullyEvolvePokemon(None, selected, trainer_class or 0)
            # DEBUG: Log selection for first few trainers
            if trainer_id <= 5:
                print(f"DEBUG: Trainer {trainer_id}, same type selected: {selected}")
            return selected

    # PRIORITY 2: Type Themes (based on trainer class)
    if use_type_themes and trainer_class:
        trainer_type_themes = GetTrainerTypeThemes()
        if trainer_class in trainer_type_themes:
            theme_types = trainer_type_themes[trainer_class]
            # Filter Pokemon by theme types directly from project database
            theme_filtered = []
            for species_id in range(1, max_species + 1):
                if species_id in existing_species:
                    continue
                if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
                    continue

                # CORREÇÃO CRÍTICA: Bloquear lendários e míticos
                if IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id):
                    continue

                pokemon_data = project_pokemon_data.get(species_id)
                if pokemon_data:
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')

                    # Check if Pokemon matches theme types
                    if type1 in theme_types or type2 in theme_types:
                        theme_filtered.append(species_id)

            if theme_filtered:
                selected = random.choice(theme_filtered)
                if force_fully_evolved and level >= force_fully_evolved_level:
                    selected = FullyEvolvePokemon(None, selected, trainer_class or 0)
                return selected

    # PRIORITY 3: Similar Strength (BST-based matching)
    if use_similar_strength:
        # Get original BST for comparison (use first Pokemon from original party as reference)
        reference_bst = None
        if project_pokemon_data:
            # Use average BST of original party as reference
            total_bst = 0
            valid_pokemon = 0
            for species_id in range(1, max_species + 1):
                if species_id in project_pokemon_data:
                    pokemon_data = project_pokemon_data[species_id]
                    bst = pokemon_data.get('bst', 0)
                    if bst > 0:
                        total_bst += bst
                        valid_pokemon += 1

            if valid_pokemon > 0:
                reference_bst = total_bst // valid_pokemon  # Average BST as reference

        if reference_bst:
            tolerance_value = int(reference_bst * bst_tolerance / 100)
            strength_filtered = []

            for species_id in range(1, max_species + 1):
                if species_id in existing_species:
                    continue
                if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
                    continue

                # CORREÇÃO CRÍTICA: Bloquear lendários e míticos
                if IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id):
                    continue

                pokemon_data = project_pokemon_data.get(species_id)
                if pokemon_data:
                    candidate_bst = pokemon_data.get('bst', 0)
                    if candidate_bst > 0 and abs(candidate_bst - reference_bst) <= tolerance_value:
                        strength_filtered.append(species_id)

            if strength_filtered:
                selected = random.choice(strength_filtered)
                if force_fully_evolved and level >= force_fully_evolved_level:
                    selected = FullyEvolvePokemon(None, selected, trainer_class or 0)
                return selected

    # PRIORITY 4: Fallback - any valid Pokemon
    fallback_candidates = []
    for species_id in range(1, max_species + 1):
        if species_id not in existing_species:
            if species_id in project_pokemon_data:
                if not (ShouldBlockMegaGigantamax() and IsSpecialForm(species_id)):
                    # CORREÇÃO CRÍTICA: Bloquear lendários e míticos
                    if not (IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id)):
                        fallback_candidates.append(species_id)

    if fallback_candidates:
        selected = random.choice(fallback_candidates)
        if force_fully_evolved and level >= force_fully_evolved_level:
            selected = FullyEvolvePokemon(None, selected, trainer_class or 0)
        return selected

    return None

def ApplyLegendarySubstitution(rom, party_offset, current_party_size, original_party_size, trainer_class, trainer_id, primary_type, secondary_type, party_types):
    """
    STEP 5: Legendary Substitution (if enabled)
    Replace ONE additional Pokemon with a legendary with matching type and LOWEST BST
    """
    import struct
    import random

    try:
        # Determine trainer category
        category = GetTrainerCategory(trainer_class, trainer_id)

        # Check if legendaries are allowed for this category
        max_legendaries = GetMaxLegendariesForTrainer(trainer_class, trainer_id)
        if max_legendaries == 0:
            return None

        # Check if legendary addition is enabled for this category
        if not ShouldAllowLegendariesInAdditionalSlot(category):
            return None

        # Get legendary Pokemon list with type matching
        legendary_candidates = GetLegendaryPokemonWithType(primary_type, secondary_type, party_types)
        if not legendary_candidates:
            return None

        # Select legendary with LOWEST BST among candidates
        project_pokemon_data = LoadProjectPokemonDatabase()
        if not project_pokemon_data:
            return None

        best_legendary = None
        lowest_bst = float('inf')

        for legendary_id in legendary_candidates:
            if legendary_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[legendary_id]
                bst = pokemon_data.get('bst', 0)
                if bst > 0 and bst < lowest_bst:
                    lowest_bst = bst
                    best_legendary = legendary_id

        if not best_legendary:
            return None

        # Replace ONE additional Pokemon (randomly select which additional slot)
        additional_slots = current_party_size - original_party_size
        if additional_slots <= 0:
            return None

        # Select random additional slot to replace
        slot_to_replace = original_party_size + random.randint(0, additional_slots - 1)
        pokemon_offset = party_offset + (slot_to_replace * 8)

        # Read current Pokemon data
        rom.seek(pokemon_offset)
        pokemon_data = bytearray(rom.read(8))

        if len(pokemon_data) < 8:
            return None

        # Replace with legendary
        struct.pack_into('<H', pokemon_data, 4, best_legendary)
        rom.seek(pokemon_offset)
        rom.write(pokemon_data)

        return best_legendary

    except Exception as e:
        return None

def GetLegendaryPokemonWithType(primary_type, secondary_type, party_types):
    """Get legendary Pokemon that match the given types"""

    # Complete legendary Pokemon list from user's memory
    legendary_pokemon = {
        # CORRECTED: Using Project Species IDs instead of National Dex IDs
        # Gen I-II Legendaries
        151,

        # Gen III Legendaries
        243, 244, 245, 249, 250, 251,

        # Gen IV Legendaries
        401, 402, 403, 409,

        # Gen V Legendaries
        533, 534, 535, 538, 539, 542, 543, 544, 547,

        # Gen VI Legendaries
        691, 692, 693, 697, 706, 718, 719,

        # Gen VII Legendaries
        746, 750, 753, 754, 755, 756, 757,

        # Gen VIII Legendaries
        825, 829, 830, 834, 838, 883,

        # Gen IX+ Legendaries (including Paradox)
        907, 908, 909, 910, 911, 918, 919, 920, 1002, 1003,
        1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013,
        1014, 1015, 1016, 1019, 1042, 1073, 1075, 1078, 1081, 1083,
        1101, 1182, 1183, 1186, 1187, 1188, 1189, 1205, 1206, 1207,
        1209, 1211, 1221, 1222, 1223, 1273, 1293, 1391, 1392, 1393,
        1394, 1395, 1398, 1399, 1400, 1405, 1406, 1407, 1408,
        # NOTE: 1396 (GIMMIGHOUL) and 1397 (GIMMIGHOUL_ROAMING) are NOT legendary - removed from both lists

    }

    # Load project Pokemon database for type checking
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        return []

    matching_legendaries = []

    for legendary_id in legendary_pokemon:
        if legendary_id in project_pokemon_data:
            pokemon_data = project_pokemon_data[legendary_id]
            type1 = pokemon_data.get('type1')
            type2 = pokemon_data.get('type2')

            # Check for type match
            if (type1 == primary_type or type2 == primary_type or
                (secondary_type is not None and (type1 == secondary_type or type2 == secondary_type)) or
                type1 in party_types or type2 in party_types):
                matching_legendaries.append(legendary_id)

    return matching_legendaries

def ExpandTrainerPartyWithCachedData(rom, trainer_offset, trainer_id, trainer_info, expansion_settings, pokemon_stats_offset):
    """
    Party expansion using cached original data - creates actual Pokemon slots
    """
    import struct

    try:
        trainer_class = trainer_info['trainer_class']
        original_party = trainer_info['original_party']
        original_party_size = len(original_party)
        party_offset = trainer_info['party_offset']

        # Determine trainer category
        category = GetTrainerCategory(trainer_class, trainer_id)

        # Determine how many Pokemon to add
        additional_count = 0
        if category == "REGULAR":
            additional_count = expansion_settings['regular']
        elif category == "IMPORTANT":
            additional_count = expansion_settings['important']
        elif category == "BOSS":
            additional_count = expansion_settings['boss']

        if additional_count == 0:
            return 0

        # Read current trainer data from modified ROM
        rom.seek(trainer_offset)
        trainer_data = bytearray(rom.read(40))
        if len(trainer_data) < 40:
            return 0

        current_party_size = trainer_data[32]

        # CRITICAL FIX: Calculate actual additional Pokemon respecting 6-Pokemon limit
        max_party_size = 6  # Absolute maximum
        actual_additional = min(additional_count, max_party_size - original_party_size)
        target_party_size = original_party_size + actual_additional

        # CRITICAL: Never exceed party size limits
        if actual_additional <= 0:
            return 0  # No room for additional Pokemon

        # CORREÇÃO: Verificar se já atingiu o tamanho máximo esperado
        # Se current_party_size >= target_party_size, já foi totalmente expandido
        if current_party_size >= target_party_size:
            return 1  # Already fully expanded, consider it successful

        # Find the lowest level Pokemon from original party to use as template
        template_level = 20  # Default
        if original_party:
            template_level = min(pokemon['level'] for pokemon in original_party)

        # CORREÇÃO: Fazer seleção inteligente de Pokémon baseada em tipos
        # Primeiro, analisar tipos da party original
        party_type_analysis = AnalyzeOriginalPartyTypesFromCachedData(original_party)

        # Selecionar Pokémon adequados - use actual_additional, not max_party_size calculation
        additional_pokemon_count = target_party_size - current_party_size

        # Gym leaders para debug
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        # DEBUG: Log expansion details for gym leaders
        if trainer_id in gym_leader_names:
            print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Expansion - Original: {original_party_size}, Current: {current_party_size}, Target: {target_party_size}, Adding: {additional_pokemon_count}")

        # CORREÇÃO CRÍTICA: Usar APENAS dados do projeto, não ROM
        # Use the same logic as SelectAdditionalPokemonWithoutRandomization for consistency
        selected_pokemon = SelectAdditionalPokemonWithoutRandomization(
            None, party_type_analysis, trainer_class, trainer_id,
            category, additional_pokemon_count, template_level, pokemon_stats_offset)

        # DEBUG: Log para gym leaders
        if trainer_id in gym_leader_names:
            print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Selecionados {len(selected_pokemon)} Pokémon: {selected_pokemon}")

        # Apply legendary substitution if enabled and RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE
        randomize_only_additional = expansion_settings.get('randomize_only_additional', False)
        if randomize_only_additional and selected_pokemon:
            # Check if legendary substitution should be applied
            max_legendaries = GetMaxLegendariesForTrainer(trainer_class, trainer_id)
            if max_legendaries > 0 and ShouldAllowLegendariesInAdditionalSlot(category):
                # Get legendary Pokemon with matching types
                primary_type = party_type_analysis.get('primary_type')
                secondary_type = party_type_analysis.get('secondary_type')
                party_types = party_type_analysis.get('party_types', set())

                legendary_candidates = GetLegendaryPokemonWithType(primary_type, secondary_type, party_types)
                if legendary_candidates:
                    # Select weakest legendary
                    project_pokemon_data = LoadProjectPokemonDatabase()
                    if project_pokemon_data:
                        best_legendary = None
                        lowest_bst = float('inf')

                        for legendary_id in legendary_candidates:
                            if legendary_id in project_pokemon_data:
                                pokemon_data = project_pokemon_data[legendary_id]
                                bst = pokemon_data.get('bst', 0)
                                if bst > 0 and bst < lowest_bst:
                                    lowest_bst = bst
                                    best_legendary = legendary_id

                        if best_legendary:
                            # Replace the last selected Pokemon with the legendary
                            selected_pokemon[-1] = best_legendary
                            if trainer_id in gym_leader_names:
                                print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Legendary substitution - #{best_legendary} (BST: {lowest_bst})")

        # Create additional Pokemon slots with selected species (including legendary substitution)
        for i, selected_species in enumerate(selected_pokemon):
            slot_index = current_party_size + i
            new_pokemon_offset = party_offset + (slot_index * 8)

            # Create a basic Pokemon entry (8 bytes)
            # Fire Red format: [IV, Padding, Level, Padding, Species (2 bytes), Held Item (2 bytes)]
            new_pokemon_data = bytearray(8)
            new_pokemon_data[0] = 0  # IV
            new_pokemon_data[1] = 0  # Padding
            new_pokemon_data[2] = template_level  # Level
            new_pokemon_data[3] = 0  # Padding
            struct.pack_into('<H', new_pokemon_data, 4, selected_species)  # Species selecionado
            struct.pack_into('<H', new_pokemon_data, 6, 0)  # Held Item (none)

            # DEBUG: Log para gym leaders
            if trainer_id in gym_leader_names:
                print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Inserindo #{selected_species} no slot {slot_index+1}")

            # Write the new Pokemon data to ROM
            rom.seek(new_pokemon_offset)
            rom.write(new_pokemon_data)

        # Update party size in trainer data - use target_party_size, not max_party_size
        trainer_data[32] = target_party_size
        rom.seek(trainer_offset)
        rom.write(trainer_data)

        return 1  # Successfully expanded

    except Exception as e:
        return 0

def SelectAdditionalPokemonFromProjectOnly(party_type_analysis, trainer_class, trainer_id, category, max_additional, base_level):
    """Seleciona Pokémon adicionais usando APENAS dados do projeto (não ROM)"""

    try:
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        if not project_pokemon_data:
            print(f"❌ Falha ao carregar banco de dados do projeto")
            return []

        # Extrair tipos da análise
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())

        # Gym leaders para debug
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        if trainer_id in gym_leader_names:
            print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Selecionando do PROJETO - Primary: {primary_type}, Secondary: {secondary_type}, Party types: {party_types}")

        selected_pokemon = []
        existing_species = set()

        # Selecionar Pokémon que combinam com os tipos da party
        for attempt in range(max_additional):
            best_species = None
            best_score = -1

            # Procurar o melhor Pokémon disponível
            for species_id, pokemon_data in project_pokemon_data.items():
                if species_id in existing_species:
                    continue

                if not (1 <= species_id <= 1440):
                    continue

                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')

                # Calcular score de compatibilidade
                score = 0

                # Prioridade 1: Tipos exatos da party
                if type1 in party_types:
                    score += 10
                if type2 is not None and type2 in party_types:
                    score += 10

                # Prioridade 2: Tipo primário
                if type1 == primary_type:
                    score += 5
                if type2 == primary_type:
                    score += 5

                # Prioridade 3: Tipo secundário
                if type1 == secondary_type:
                    score += 3
                if type2 == secondary_type:
                    score += 3

                # Apenas selecionar se tem alguma compatibilidade
                if score > 0 and score > best_score:
                    best_species = species_id
                    best_score = score

            if best_species:
                selected_pokemon.append(best_species)
                existing_species.add(best_species)

                if trainer_id in gym_leader_names:
                    pokemon_data = project_pokemon_data[best_species]
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')
                    print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Selecionado #{best_species} (Type1={type1}, Type2={type2}, Score={best_score})")
            else:
                # Se não encontrar compatível, usar Pokémon aleatório do projeto
                available_species = [s for s in project_pokemon_data.keys() if s not in existing_species and 1 <= s <= 1440]
                if available_species:
                    import random
                    fallback_species = random.choice(available_species)
                    selected_pokemon.append(fallback_species)
                    existing_species.add(fallback_species)

                    if trainer_id in gym_leader_names:
                        print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Fallback #{fallback_species}")

        return selected_pokemon

    except Exception as e:
        print(f"❌ Erro em SelectAdditionalPokemonFromProjectOnly: {e}")
        import traceback
        traceback.print_exc()
        return []

def AnalyzeOriginalPartyTypesFromCachedData(original_party):
    """Analisa tipos da party original usando dados cached"""
    from collections import Counter

    if not original_party:
        return {'primary_type': None, 'secondary_type': None, 'party_types': set(), 'type_counts': Counter()}

    # Carregar dados do projeto
    project_pokemon_data = LoadProjectPokemonDatabase()

    type_counts = Counter()
    party_types = set()

    for pokemon in original_party:
        species = pokemon.get('species', 1)

        if species in project_pokemon_data:
            pokemon_data = project_pokemon_data[species]
            type1 = pokemon_data.get('type1')
            type2 = pokemon_data.get('type2')

            if type1 is not None:
                type_counts[type1] += 1
                party_types.add(type1)

            if type2 is not None and type2 != type1:
                type_counts[type2] += 1
                party_types.add(type2)

    # Determinar tipos predominantes
    most_common = type_counts.most_common(2)
    primary_type = most_common[0][0] if most_common else None
    secondary_type = most_common[1][0] if len(most_common) > 1 else None

    return {
        'primary_type': primary_type,
        'secondary_type': secondary_type,
        'party_types': party_types,
        'type_counts': type_counts
    }

def RandomizeAllTrainerPokemonWithCache(rom, party_offset, current_party_size, original_party, party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id):
    """
    Randomize all Pokemon in trainer party using cached data
    """
    import struct
    import random

    try:
        modifications = 0
        existing_species = set()

        # Get predominant types for filtering candidates
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())

        # Randomize all Pokemon slots
        for i in range(current_party_size):
            pokemon_offset = party_offset + (i * 8)

            # Read current data from ROM
            rom.seek(pokemon_offset)
            pokemon_data = bytearray(rom.read(8))

            if len(pokemon_data) < 8:
                continue

            level = pokemon_data[2]
            current_species = struct.unpack('<H', pokemon_data[4:6])[0]

            # Validate data - CORREÇÃO: Aceitar levels >100 da ROM original
            if level == 0 or level > 255 or current_species == 0:
                continue

            # Select new Pokemon from project database
            new_species = SelectPokemonFromProjectDatabase(
                primary_type, secondary_type, party_types, existing_species,
                level, trainer_class, trainer_id)

            if new_species and new_species != current_species:
                # Update species in ROM
                struct.pack_into('<H', pokemon_data, 4, new_species)
                rom.seek(pokemon_offset)
                rom.write(pokemon_data)

                # Add to existing species to prevent duplicates
                existing_species.add(new_species)
                modifications += 1

        return modifications

    except Exception as e:
        return 0

def FindTrainerTable(rom):
    """Encontra a tabela de treinadores na ROM"""
    import struct

    # Para Fire Red, procurar pelo padrão da tabela de treinadores
    # Baseado no randomizer Java: procurar por ponteiros para dados de treinador

    # Offset específico para Fire Red (U) 1.0 (sempre usamos esta versão)
    # Do arquivo .randomizer/config/gen3_offsets.ini: TrainerData=0x23EAC8
    fire_red_1_0_offset = 0x23EAC8

    print("  Using Fire Red (U) 1.0 trainer table offset...")

    # Primeiro, tentar o offset exato
    if IsValidTrainerTable(rom, fire_red_1_0_offset):
        print(f"    ✅ Fire Red 1.0 trainer table found at 0x{fire_red_1_0_offset:08X}")
        return fire_red_1_0_offset

    # Se não funcionou, tentar offsets próximos (pode ter sido expandido)
    nearby_offsets = [
        fire_red_1_0_offset,
        fire_red_1_0_offset + 0x28,  # Possível deslocamento
        fire_red_1_0_offset - 0x28,  # Possível deslocamento
        0x23EB38,  # Fire Red (U) 1.1 como fallback
    ]

    for offset in nearby_offsets:
        if IsValidTrainerTable(rom, offset):
            return offset

    # Se não encontrou nos offsets conhecidos, fazer busca por padrão
    trainer_offset = FindTrainerTableByPattern(rom)
    if trainer_offset > 0:
        return trainer_offset

    return 0

def IsValidTrainerTable(rom, offset):
    """Verifica se o offset contém uma tabela de treinadores válida para Fire Red"""
    import struct

    try:
        rom.seek(offset)

        # Fire Red tem 107 classes de treinador (conforme gen3_offsets.ini)
        # TrainerClassCount=107
        max_trainer_class = 107

        # Ler várias entradas para validar
        valid_entries = 0
        for i in range(10):  # Verificar 10 entradas
            trainer_data = rom.read(40)  # 40 bytes por treinador (TrainerEntrySize=40)
            if len(trainer_data) < 40:
                return False

            # Estrutura do treinador Fire Red:
            # 0: party_flags (0-3)
            # 1: trainer_class (1-107)
            # 2-3: encounter_music_gender
            # 4-7: trainer_pic
            # 8-19: trainer_name (12 bytes)
            # 20-23: items[4]
            # 24-31: ai_flags
            # 32: party_size (1-6)
            # 33-35: padding
            # 36-39: party_pointer

            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]

            # Validações específicas para Fire Red
            if not (0 <= party_flags <= 3):  # Flags válidas
                continue

            if not (1 <= trainer_class <= max_trainer_class):  # Classes válidas
                continue

            if not (1 <= party_size <= 6):  # Tamanho de equipe válido
                continue

            # Ponteiro deve ser válido (ROM address)
            if not IsValidPointer(party_ptr):
                continue

            valid_entries += 1

        # Pelo menos 7 das 10 entradas devem ser válidas
        return valid_entries >= 7

    except:
        return False

def FindTrainerTableByPattern(rom):
    """Busca a tabela de treinadores por padrão de ponteiros"""
    import struct

    # Procurar por sequências que parecem ponteiros para dados de treinador
    rom.seek(0)
    rom_data = rom.read()

    for i in range(0x20000, len(rom_data) - 40, 4):  # Começar em 0x20000
        try:
            # Ler como possível entrada de treinador
            trainer_data = rom_data[i:i+40]
            if len(trainer_data) < 40:
                continue

            # Verificar estrutura básica
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]

            # Validações básicas
            if not (0 <= party_flags <= 3):  # Flags válidas
                continue

            if not (1 <= trainer_class <= 50):  # Classes válidas
                continue

            if not (1 <= party_size <= 6):  # Tamanho de equipe válido
                continue

            # Ler ponteiro para dados da equipe
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]

            if not IsValidPointer(party_ptr):
                continue

            # Verificar se há mais entradas válidas consecutivas
            valid_consecutive = 0
            for j in range(1, 10):  # Verificar próximas 9 entradas
                next_offset = i + (j * 40)
                if next_offset + 40 > len(rom_data):
                    break

                next_trainer = rom_data[next_offset:next_offset+40]
                next_class = next_trainer[1]
                next_party_size = next_trainer[32]

                if 1 <= next_class <= 50 and 1 <= next_party_size <= 6:
                    valid_consecutive += 1
                else:
                    break

            # Se encontrou pelo menos 5 entradas válidas consecutivas
            if valid_consecutive >= 5:
                return i

        except:
            continue

    return 0

def FindTrainerTableManually(rom):
    """Busca manual mais ampla pela tabela de treinadores"""
    import struct

    # Busca mais ampla por estruturas que parecem treinadores
    rom.seek(0)
    rom_data = rom.read()

    for i in range(0x10000, min(len(rom_data) - 1000, 0x400000), 0x1000):  # Busca a cada 4KB
        try:
            # Verificar se há estruturas de treinador nesta região
            valid_trainers = 0

            for j in range(0, 1000, 40):  # Verificar 25 possíveis entradas
                offset = i + j
                if offset + 40 > len(rom_data):
                    break

                trainer_data = rom_data[offset:offset+40]
                trainer_class = trainer_data[1]
                party_size = trainer_data[32]

                if 1 <= trainer_class <= 50 and 1 <= party_size <= 6:
                    # Verificar ponteiro
                    party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                    if IsValidPointer(party_ptr):
                        valid_trainers += 1

            # Se encontrou muitos treinadores válidos nesta região
            if valid_trainers >= 10:
                return i

        except:
            continue

    return 0

def RandomizeTrainerIfEligibleWithOriginalData(original_rom, rom, trainer_offset, trainer_id, pokemon_stats_offset):
    """Randomiza um treinador se ele for elegível, usando ROM original para análise"""
    import struct

    try:
        # LER DADOS DO TREINADOR DA ROM ORIGINAL
        original_rom.seek(trainer_offset)
        trainer_data = bytearray(original_rom.read(40))

        if len(trainer_data) < 40:
            return False

        trainer_class = trainer_data[1]
        party_size = trainer_data[32]

        # Verificar se é elegível para randomização
        if not IsTrainerEligibleForRandomization(trainer_class, trainer_id):
            return False

        # Validar party size
        if party_size == 0 or party_size > 6:
            return False

        # Ler ponteiro para a equipe do treinador
        party_ptr = struct.unpack('<I', trainer_data[36:40])[0]

        if not IsValidPointer(party_ptr):
            return False

        # Validar offset da equipe
        party_offset = party_ptr - 0x08000000
        original_rom.seek(0, 2)  # Ir para o final
        rom_size = original_rom.tell()
        if party_offset < 0 or party_offset >= rom_size - (party_size * 8):
            return False

        # RANDOMIZAR A EQUIPE DO TREINADOR USANDO DADOS ORIGINAIS
        modifications = RandomizeTrainerPartyWithOriginalData(
            original_rom, rom, party_offset, party_size, pokemon_stats_offset, trainer_class, trainer_id)

        return modifications > 0

    except (struct.error, IOError, ValueError):
        # Specific error handling for known issues
        return False
    except Exception:
        # Catch-all for unexpected errors
        return False

def RandomizeTrainerIfEligible(rom, trainer_offset, trainer_id, pokemon_stats_offset):
    """Função antiga mantida para compatibilidade"""
    return RandomizeTrainerIfEligibleWithOriginalData(rom, rom, trainer_offset, trainer_id, pokemon_stats_offset)

def IsTrainerEligibleForRandomization(trainer_class, trainer_id):
    """Verifica se um treinador é elegível para randomização baseado na configuração"""

    # DADOS CORRETOS DO .randomizer/constants/Gen3Constants.java trainerTagsFRLG para Fire Red:

    # Gym Leaders (IDs específicos do Gen3Constants.java trainerTagsFRLG)
    gym_leader_ids = [0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2, 0x1A4, 0x1A3, 0x15E]  # Brock, Misty, Lt.Surge, Erika, Koga, Sabrina, Blaine, Giovanni

    # Elite Four Round 1 e Round 2 (do Gen3Constants.java trainerTagsFRLG)
    elite_four_ids = [
        0x19A, 0x19B, 0x19C, 0x19D,  # ELITE1-1, ELITE2-1, ELITE3-1, ELITE4-1 (Round 1)
        0x2DF, 0x2E0, 0x2E1, 0x2E2   # ELITE1-2, ELITE2-2, ELITE3-2, ELITE4-2 (Round 2)
    ]

    # Giovanni adicional (GIO1-LEADER, GIO2-LEADER)
    giovanni_ids = [0x15C, 0x15D]

    # Rival IDs (do Gen3Constants.java trainerTagsFRLG - RIVAL1 a RIVAL9)
    rival_ids = [
        # RIVAL1 (Initial)
        0x148, 0x146, 0x147,
        # RIVAL2 (Route 22 weak)
        0x14B, 0x149, 0x14A,
        # RIVAL3 (Cerulean)
        0x14E, 0x14C, 0x14D,
        # RIVAL4 (SS Anne)
        0x1A9, 0x1A7, 0x1A8,
        # RIVAL5 (Pokemon Tower)
        0x1AF, 0x1AD, 0x1AE,
        # RIVAL6 (Silph Co)
        0x1B2, 0x1B0, 0x1B1,
        # RIVAL7 (Route 22 strong)
        0x1B5, 0x1B3, 0x1B4,
        # RIVAL8 (E4 Round 1)
        0x1B8, 0x1B6, 0x1B7,
        # RIVAL9 (E4 Round 2)
        0x2E5, 0x2E3, 0x2E4
    ]

    # Doubles Trainer Classes (do gen3_offsets.ini)
    doubles_classes = [26, 40, 52, 53, 54, 92, 93, 94, 95, 96]

    # Verificar configuração
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Verificar se deve randomizar bosses (Gym Leaders + Elite Four + Giovanni)
        randomize_bosses = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        if (trainer_id in gym_leader_ids or trainer_id in elite_four_ids or trainer_id in giovanni_ids) and not randomize_bosses:
            return False

        # Verificar se deve randomizar rivais
        randomize_important = "RANDOMIZE_IMPORTANT_TRAINERS TRUE" in content
        if trainer_id in rival_ids and not randomize_important:
            return False

        # Verificar se deve randomizar treinadores regulares
        randomize_regular = "RANDOMIZE_REGULAR_TRAINERS TRUE" in content

        # Se não é boss nem rival, é treinador regular
        is_regular = (trainer_id not in gym_leader_ids and
                     trainer_id not in elite_four_ids and
                     trainer_id not in giovanni_ids and
                     trainer_id not in rival_ids)

        if is_regular:
            return randomize_regular

        return True

    except:
        # Se não conseguir ler configuração, usar padrão conservador
        # Não randomizar bosses nem rivais por padrão
        return (trainer_id not in gym_leader_ids and
                trainer_id not in elite_four_ids and
                trainer_id not in giovanni_ids and
                trainer_id not in rival_ids)

def RandomizeTrainerPartyWithOriginalData(original_rom, rom, party_offset, party_size, pokemon_stats_offset, trainer_class=None, trainer_id=None):
    """
    CORRECTED IMPLEMENTATION: Follows the exact 5-step sequence for trainer randomization
    """
    import random
    import struct

    try:
        # STEP 1: ROM Data Reading (One-time only)
        original_party_data = ReadOriginalTrainerPartyData(original_rom, party_offset, party_size)
        if not original_party_data:
            return 0

        # STEP 2: Type Analysis
        party_type_analysis = AnalyzeOriginalPartyTypes(original_party_data)

        # STEP 3: Check RANDOMIZE_ONLY_ADDITIONAL_POKEMON configuration
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()

        if randomize_only_additional:
            # CRITICAL: NEVER modify original party members
            return RandomizeOnlyAdditionalPokemonCorrected(
                original_rom, rom, party_offset, party_size, original_party_data,
                party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id)
        else:
            # Randomize all Pokemon in party
            return RandomizeAllTrainerPokemon(
                original_rom, rom, party_offset, party_size, original_party_data,
                party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id)

    except Exception as e:
        return 0

# Duplicate function removed - using the first implementation above

def AnalyzeTrainerPredominantTypes(party_data):
    """
    STEP 2: Analyze predominant types from original party data
    Priority order: Primary type > Secondary type > Most frequent type across party
    """
    if not party_data:
        return {'primary_type': None, 'secondary_type': None, 'party_types': set()}

    # Load project Pokemon data to get type information
    project_pokemon_data = LoadProjectPokemonDatabase()

    type_counts = {}
    party_types = set()

    for pokemon in party_data:
        species_id = pokemon['species']
        if species_id in project_pokemon_data:
            pokemon_info = project_pokemon_data[species_id]
            type1 = pokemon_info.get('type1')
            type2 = pokemon_info.get('type2')

            if type1 is not None:
                type_counts[type1] = type_counts.get(type1, 0) + 1
                party_types.add(type1)

            if type2 is not None and type2 != type1:
                type_counts[type2] = type_counts.get(type2, 0) + 1
                party_types.add(type2)

    # Determine predominant types
    if type_counts:
        # Sort by frequency, then by type value for consistency
        sorted_types = sorted(type_counts.items(), key=lambda x: (-x[1], x[0]))
        primary_type = sorted_types[0][0]
        secondary_type = sorted_types[1][0] if len(sorted_types) > 1 else None
    else:
        primary_type = None
        secondary_type = None

    return {
        'primary_type': primary_type,
        'secondary_type': secondary_type,
        'party_types': party_types,
        'type_counts': type_counts
    }

def ApplyCorrectedRandomizationWorkflow(all_trainer_data, trainer_type_analysis, project_pokemon_data):
    """
    STEP 4: Apply corrected randomization workflow
    Respects RANDOMIZE_ONLY_ADDITIONAL_POKEMON configuration
    """
    results = {
        'modified_trainers': 0,
        'randomized_pokemon': 0,
        'preserved_original': 0
    }

    # Read configuration
    randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()

    # Apply party expansion first
    expansion_settings = LoadPartyExpansionSettings()
    if expansion_settings['enabled']:
        print(f"📈 Applying party expansion: Regular+{expansion_settings['regular']}, Important+{expansion_settings['important']}, Boss+{expansion_settings['boss']}")
        # Party expansion logic would go here
        # This modifies the ROM to add additional Pokemon slots

    # Apply randomization based on configuration
    for trainer_id, trainer_data in all_trainer_data.items():
        if not IsTrainerRandomizable(trainer_data['class'], trainer_id):
            continue

        type_analysis = trainer_type_analysis.get(trainer_id, {})
        original_party_size = len(trainer_data['party'])

        if randomize_only_additional:
            # CRITICAL: NEVER modify original party members
            # Only randomize additional Pokemon slots (if any exist after expansion)
            current_party_size = GetCurrentTrainerPartySize(trainer_id)  # After expansion
            additional_slots = current_party_size - original_party_size

            if additional_slots > 0:
                # Randomize only the additional slots
                randomized = RandomizeAdditionalSlotsOnly(
                    trainer_id, trainer_data, type_analysis, project_pokemon_data, additional_slots)
                results['randomized_pokemon'] += randomized
                results['preserved_original'] += original_party_size
            else:
                results['preserved_original'] += original_party_size
        else:
            # Randomize all Pokemon in party (original behavior)
            randomized = RandomizeAllTrainerPokemon(
                trainer_id, trainer_data, type_analysis, project_pokemon_data)
            results['randomized_pokemon'] += randomized

        results['modified_trainers'] += 1

    return results

def GetCurrentTrainerPartySize(trainer_id):
    """Get current party size after expansion (placeholder - would read from ROM)"""
    # This would read the current party size from the ROM after expansion
    # For now, return a placeholder value
    return 6  # Placeholder

def RandomizeAdditionalSlotsOnly(trainer_id, trainer_data, type_analysis, project_pokemon_data, additional_slots):
    """
    STEP 4: Randomize ONLY additional Pokemon slots, preserving original party
    """
    randomized_count = 0

    # Get predominant types for filtering
    primary_type = type_analysis.get('primary_type')
    secondary_type = type_analysis.get('secondary_type')
    party_types = type_analysis.get('party_types', set())

    # Select Pokemon for additional slots based on type analysis
    for slot in range(additional_slots):
        # Use the corrected Pokemon selection with proper hierarchy
        selected_species = SelectPokemonForAdditionalSlot(
            primary_type, secondary_type, party_types, project_pokemon_data, trainer_data)

        if selected_species:
            # Apply the selected Pokemon to the additional slot in ROM
            # This would write to the ROM at the appropriate offset
            randomized_count += 1

    return randomized_count

def SelectPokemonForAdditionalSlot(primary_type, secondary_type, party_types, project_pokemon_data, trainer_data):
    """
    STEP 3: Pokemon Selection from Project Database using corrected hierarchy
    """
    trainer_class = trainer_data.get('class', 0)
    trainer_category = trainer_data.get('category', 'REGULAR')

    # Apply the corrected selection hierarchy
    # Use a placeholder species for selection (will be replaced)
    placeholder_species = 1  # Bulbasaur as placeholder
    selected_species = ApplyTrainerPokemonRandomizationWithProject(
        placeholder_species, 25, project_pokemon_data, trainer_class)  # Correct parameter order

    return selected_species

def DetermineTrainerCategory(trainer_class, trainer_id):
    """Determine trainer category for legendary limits and other rules"""
    # Gym Leaders, Elite Four, Champion
    boss_classes = [14, 15, 16, 17, 18, 19, 20, 21]  # Gym leader classes
    boss_trainer_ids = [389, 390, 391, 392, 393]  # Elite Four + Champion

    # Rivals and important trainers
    important_classes = [42]  # Rival class
    important_trainer_ids = [326, 327, 328]  # Specific rival encounters

    if trainer_class in boss_classes or trainer_id in boss_trainer_ids:
        return "BOSS"
    elif trainer_class in important_classes or trainer_id in important_trainer_ids:
        return "IMPORTANT"
    else:
        return "REGULAR"

def IsTrainerRandomizable(trainer_class, trainer_id):
    """
    Check if trainer should be randomized based on configuration
    CRITICAL: ALL trainers are eligible, including single-Pokemon trainers
    """
    # Exclude specific trainers that should never be randomized
    excluded_trainer_ids = {
        # Tutorial trainers or special cases can be added here
        # For now, ALL trainers are eligible for randomization
    }

    excluded_trainer_classes = {
        # Special trainer classes that should be excluded can be added here
        # For now, ALL trainer classes are eligible
    }

    if trainer_id in excluded_trainer_ids:
        return False

    if trainer_class in excluded_trainer_classes:
        return False

    # CRITICAL: ALL trainers are eligible, even those with only 1 Pokemon
    return True

def ReadOriginalTrainerPartyData(original_rom, party_offset, party_size):
    """STEP 1: Read trainer party data from ROM ONCE to obtain original composition"""
    import struct

    try:
        original_rom.seek(party_offset)
        party_data = original_rom.read(party_size * 8)  # 8 bytes per Pokemon

        if len(party_data) < party_size * 8:
            return None

        original_party = []
        for i in range(party_size):
            pokemon_offset = i * 8
            if pokemon_offset + 8 <= len(party_data):
                level = party_data[pokemon_offset + 2]
                species_id = struct.unpack('<H', party_data[pokemon_offset + 4:pokemon_offset + 6])[0]

                # CORREÇÃO: Aceitar levels >100 que existem na ROM original (ex: Koga level 151)
                if level > 0 and level <= 255 and species_id > 0 and species_id <= 1440:
                    original_party.append({
                        'slot': i,
                        'level': level,
                        'species': species_id
                    })

        return original_party

    except Exception:
        return None

def AnalyzeOriginalPartyTypes(original_party_data):
    """STEP 2: Analyze original party to identify predominant type(s)"""
    if not original_party_data:
        return {'primary_type': None, 'secondary_type': None, 'party_types': set()}

    # Load project Pokemon database for type information
    project_pokemon_data = LoadProjectPokemonDatabase()

    type_counts = {}
    party_types = set()

    for pokemon in original_party_data:
        species_id = pokemon['species']
        if species_id in project_pokemon_data:
            pokemon_data = project_pokemon_data[species_id]
            primary_type = pokemon_data.get('type1')
            secondary_type = pokemon_data.get('type2')

            if primary_type is not None:
                type_counts[primary_type] = type_counts.get(primary_type, 0) + 2  # Primary type gets double weight
                party_types.add(primary_type)

            if secondary_type is not None and secondary_type != primary_type:
                type_counts[secondary_type] = type_counts.get(secondary_type, 0) + 1  # Secondary type gets single weight
                party_types.add(secondary_type)

    # Determine predominant types
    if type_counts:
        sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
        primary_type = sorted_types[0][0]
        secondary_type = sorted_types[1][0] if len(sorted_types) > 1 else None
    else:
        primary_type = None
        secondary_type = None

    return {
        'primary_type': primary_type,
        'secondary_type': secondary_type,
        'party_types': party_types,
        'type_counts': type_counts
    }

def RandomizeOnlyAdditionalPokemonCorrected(original_rom, rom, party_offset, party_size, original_party_data, party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id):
    """
    STEP 4: CORRECTED - Randomize ONLY additional Pokemon slots, NEVER modify original party members
    """
    import struct
    import random

    try:
        modifications = 0
        original_party_size = len(original_party_data)

        # Determine how many additional Pokemon slots exist
        additional_slots = party_size - original_party_size
        if additional_slots <= 0:
            return 0  # No additional slots to randomize

        # STEP 3: Pokemon Selection from Project Database
        project_pokemon_data = LoadProjectPokemonDatabase()

        # Get predominant types for filtering candidates
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())

        # Collect existing species to prevent duplicates
        existing_species = {pokemon['species'] for pokemon in original_party_data}

        # Gym leaders for logging
        gym_leader_names = {
            0x19E: "BROCK", 0x19F: "MISTY", 0x1A0: "LT_SURGE", 0x1A1: "ERIKA",
            0x1A2: "KOGA", 0x1A4: "SABRINA", 0x1A3: "BLAINE", 0x15E: "GIOVANNI"
        }

        gym_leader_additions = []

        # RANDOMIZE ONLY ADDITIONAL POKEMON SLOTS (starting from original_party_size)
        for i in range(original_party_size, party_size):
            pokemon_offset = party_offset + (i * 8)

            # Read current data from ROM
            rom.seek(pokemon_offset)
            pokemon_data = bytearray(rom.read(8))

            if len(pokemon_data) < 8:
                continue

            level = pokemon_data[2]
            current_species = struct.unpack('<H', pokemon_data[4:6])[0]

            # Validate data - CORREÇÃO: Aceitar levels >100 da ROM original
            if level == 0 or level > 255:
                continue

            # STEP 3: Select Pokemon from project database using type analysis
            new_species = SelectPokemonFromProjectDatabase(
                primary_type, secondary_type, party_types, existing_species,
                level, trainer_class, trainer_id, project_pokemon_data)

            if new_species and new_species != current_species and 1 <= new_species <= 1440:
                # Update species in ROM
                struct.pack_into('<H', pokemon_data, 4, new_species)
                rom.seek(pokemon_offset)
                rom.write(pokemon_data)

                # Add to existing species to prevent future duplicates
                existing_species.add(new_species)
                modifications += 1

                # Collect for logging
                if trainer_id in gym_leader_names:
                    is_legendary = IsTrainerPokemonLegendary(new_species)
                    gym_leader_additions.append({
                        'slot': i + 1,
                        'species': new_species,
                        'legendary': is_legendary
                    })

        # STEP 5: Legendary Substitution (if enabled)
        if gym_leader_additions:
            modifications += ApplyLegendarySubstitution(
                rom, party_offset, original_party_size, party_size,
                party_type_analysis, trainer_class, trainer_id, existing_species)

        # Simplified logging for gym leaders
        if trainer_id in gym_leader_names and gym_leader_additions:
            leader_name = gym_leader_names[trainer_id]
            additions_text = []
            for addition in gym_leader_additions:
                legendary_mark = " ⭐" if addition['legendary'] else ""
                additions_text.append(f"#{addition['species']}{legendary_mark}")

            print(f"🏆 {leader_name}: Added {', '.join(additions_text)}")

        return modifications

    except Exception as e:
        return 0

# Duplicate function removed - using the implementation at line 2048

def FindPokemonByTypeFromProject(target_type, secondary_type, project_pokemon_data, existing_species, max_species, block_legendaries, use_similar_strength, bst_tolerance, level):
    """Find Pokemon from project database by type with all filters applied"""
    candidates = []

    # Calculate target BST if similar strength is enabled
    target_bst = None
    if use_similar_strength and level > 0:
        # Estimate BST based on level (rough approximation)
        target_bst = min(600, 300 + (level * 3))  # Scale with level

    for species_id in range(1, max_species + 1):
        if species_id in existing_species:
            continue

        if species_id not in project_pokemon_data:
            continue

        pokemon_data = project_pokemon_data[species_id]

        # Type matching
        type1 = pokemon_data.get('type1')
        type2 = pokemon_data.get('type2')

        type_match = False
        if secondary_type is not None:
            # Exact dual type match
            type_match = (type1 == target_type and type2 == secondary_type) or \
                        (type1 == secondary_type and type2 == target_type)
        else:
            # Single type match
            type_match = type1 == target_type or type2 == target_type

        if not type_match:
            continue

        # Legendary/Mythical blocking
        if block_legendaries and (IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id)):
            continue

        # Mega/Gigantamax blocking
        if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
            continue

        # Similar strength filtering
        if use_similar_strength and target_bst is not None:
            pokemon_bst = pokemon_data.get('bst', 0)
            if pokemon_bst > 0:
                tolerance_value = int(target_bst * bst_tolerance / 100)
                if abs(pokemon_bst - target_bst) > tolerance_value:
                    continue

        candidates.append(species_id)

    return candidates

def RandomizeAllTrainerPokemon(original_rom, rom, party_offset, party_size, original_party_data, party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id):
    """Randomize all Pokemon in trainer party (when RANDOMIZE_ONLY_ADDITIONAL_POKEMON is FALSE)"""
    import struct
    import random

    try:
        modifications = 0
        project_pokemon_data = LoadProjectPokemonDatabase()
        existing_species = set()

        # Get predominant types for filtering candidates
        primary_type = party_type_analysis.get('primary_type')
        secondary_type = party_type_analysis.get('secondary_type')
        party_types = party_type_analysis.get('party_types', set())

        # Randomize all Pokemon slots
        for i in range(party_size):
            pokemon_offset = party_offset + (i * 8)

            # Read current data from ROM
            rom.seek(pokemon_offset)
            pokemon_data = bytearray(rom.read(8))

            if len(pokemon_data) < 8:
                continue

            level = pokemon_data[2]
            current_species = struct.unpack('<H', pokemon_data[4:6])[0]

            # Validate data - CORREÇÃO: Aceitar levels >100 da ROM original
            if level == 0 or level > 255 or current_species == 0:
                continue

            # Select new Pokemon from project database
            new_species = SelectPokemonFromProjectDatabase(
                primary_type, secondary_type, party_types, existing_species,
                level, trainer_class, trainer_id, project_pokemon_data)

            if new_species and new_species != current_species and 1 <= new_species <= 1440:
                # Update species in ROM
                struct.pack_into('<H', pokemon_data, 4, new_species)
                rom.seek(pokemon_offset)
                rom.write(pokemon_data)

                # Add to existing species to prevent future duplicates
                existing_species.add(new_species)
                modifications += 1

        # Apply legendary substitution if enabled
        modifications += ApplyLegendarySubstitution(
            rom, party_offset, 0, party_size,
            party_type_analysis, trainer_class, trainer_id, existing_species)

        return modifications

    except Exception as e:
        return 0

def FindPokemonBySimilarStrengthFromProject(project_pokemon_data, existing_species, max_species, block_legendaries, bst_tolerance, level):
    """Find Pokemon from project database by similar strength (BST)"""
    candidates = []

    # Calculate target BST based on level
    target_bst = min(600, 300 + (level * 3))  # Scale with level
    tolerance_value = int(target_bst * bst_tolerance / 100)

    for species_id in range(1, max_species + 1):
        if species_id in existing_species:
            continue

        if species_id not in project_pokemon_data:
            continue

        pokemon_data = project_pokemon_data[species_id]

        # Legendary/Mythical blocking
        if block_legendaries and (IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id)):
            continue

        # Mega/Gigantamax blocking
        if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
            continue

        # BST filtering
        pokemon_bst = pokemon_data.get('bst', 0)
        if pokemon_bst > 0 and abs(pokemon_bst - target_bst) <= tolerance_value:
            candidates.append(species_id)

    return candidates

# Duplicate function removed - using the implementation at line 2246

def FindLegendaryPokemonByType(primary_type, secondary_type, party_types, existing_species):
    """Find legendary Pokemon that match the party's type theme"""
    project_pokemon_data = LoadProjectPokemonDatabase()
    legendary_candidates = []

    # Get all legendary Pokemon
    all_legendaries = []
    for species_id in range(1, 1441):
        if IsTrainerPokemonLegendary(species_id) and species_id not in existing_species:
            all_legendaries.append(species_id)

    # Filter by type matching
    for legendary_id in all_legendaries:
        if legendary_id in project_pokemon_data:
            pokemon_data = project_pokemon_data[legendary_id]
            type1 = pokemon_data.get('type1')
            type2 = pokemon_data.get('type2')

            # Check if legendary matches party types
            if primary_type is not None and (type1 == primary_type or type2 == primary_type):
                legendary_candidates.append(legendary_id)
            elif secondary_type is not None and (type1 == secondary_type or type2 == secondary_type):
                legendary_candidates.append(legendary_id)
            elif party_types and (type1 in party_types or type2 in party_types):
                legendary_candidates.append(legendary_id)

    return legendary_candidates

def RandomizeTrainerParty(rom, party_offset, party_size, pokemon_stats_offset, trainer_class=None, trainer_id=None):
    """Função antiga mantida para compatibilidade"""
    return RandomizeTrainerPartyWithOriginalData(rom, rom, party_offset, party_size, pokemon_stats_offset, trainer_class, trainer_id)

def GetMaxLegendariesForTrainer(trainer_class, trainer_id):
    """Determina o número máximo de lendários permitidos para um treinador"""

    # Verificar configuração global primeiro
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # CORREÇÃO: Verificar configurações específicas ANTES do bloqueio global
        # Determinar categoria do treinador
        category = GetTrainerCategoryForLegendaries(trainer_class, trainer_id)

        # Extrair limites da configuração
        if category == "BOSS":
            import re
            match = re.search(r'MAX_LEGENDARIES_PER_BOSS\s+(\d+)', content)
            max_legendaries = int(match.group(1)) if match else 1  # Padrão 1 para bosses
        elif category == "IMPORTANT":
            import re
            match = re.search(r'MAX_LEGENDARIES_PER_IMPORTANT\s+(\d+)', content)
            max_legendaries = int(match.group(1)) if match else 0
        else:  # REGULAR
            import re
            match = re.search(r'MAX_LEGENDARIES_PER_REGULAR\s+(\d+)', content)
            max_legendaries = int(match.group(1)) if match else 0

        # APLICAR BLOQUEIO GLOBAL APENAS SE CONFIGURADO E SE NÃO HÁ LIMITE ESPECÍFICO
        if "TRAINER_BLOCK_LEGENDARIES TRUE" in content:
            # Se bloqueio global está ativo, respeitar apenas se não há configuração específica
            if max_legendaries == 0:  # Sem configuração específica
                return 0
            # Com configuração específica, permitir o limite configurado

        return max_legendaries

    except:
        return 0  # Padrão seguro

def GetTrainerTypeThemes():
    """Retorna o mapeamento de classes de treinador para seus type themes"""
    # Constantes de tipos baseadas no projeto
    TYPE_NORMAL = 0x00
    TYPE_FIGHTING = 0x01
    TYPE_FLYING = 0x02
    TYPE_POISON = 0x03
    TYPE_GROUND = 0x04
    TYPE_ROCK = 0x05
    TYPE_BUG = 0x06
    TYPE_GHOST = 0x07
    TYPE_STEEL = 0x08
    TYPE_FIRE = 0x0a
    TYPE_WATER = 0x0b
    TYPE_GRASS = 0x0c
    TYPE_ELECTRIC = 0x0d
    TYPE_PSYCHIC = 0x0e
    TYPE_ICE = 0x0f
    TYPE_DRAGON = 0x10
    TYPE_DARK = 0x11
    TYPE_FAIRY = 0x17

    return {
        # TRAINER_CLASS_BUG_CATCHER = 2
        2: [TYPE_BUG],
        # TRAINER_CLASS_SAILOR = 4
        4: [TYPE_WATER, TYPE_FIGHTING],
        # TRAINER_CLASS_SUPER_NERD = 8
        8: [TYPE_ELECTRIC, TYPE_PSYCHIC],
        # TRAINER_CLASS_HIKER = 9
        9: [TYPE_ROCK, TYPE_GROUND],
        # TRAINER_CLASS_BIKER = 10
        10: [TYPE_POISON],
        # TRAINER_CLASS_BURGLAR = 11
        11: [TYPE_FIRE, TYPE_POISON],
        # TRAINER_CLASS_JUGGLER = 13
        13: [TYPE_PSYCHIC, TYPE_POISON],
        # TRAINER_CLASS_FISHERMAN = 14
        14: [TYPE_WATER],
        # TRAINER_CLASS_SWIMMER_M = 15
        15: [TYPE_WATER],
        # TRAINER_CLASS_PSYCHIC_M = 19
        19: [TYPE_PSYCHIC],
        # TRAINER_CLASS_ROCKER = 20
        20: [TYPE_ELECTRIC],
        # TRAINER_CLASS_JUGGLER_2 = 21
        21: [TYPE_PSYCHIC, TYPE_POISON],
        # TRAINER_CLASS_TAMER = 22
        22: [TYPE_NORMAL],
        # TRAINER_CLASS_BIRD_KEEPER = 23
        23: [TYPE_FLYING, TYPE_NORMAL],
        # TRAINER_CLASS_BLACKBELT = 24
        24: [TYPE_FIGHTING],
        # TRAINER_CLASS_SCIENTIST = 26
        26: [TYPE_ELECTRIC, TYPE_POISON],
        # TRAINER_CLASS_GIOVANNI = 27
        27: [TYPE_GROUND, TYPE_ROCK],
        # TRAINER_CLASS_ROCKET = 28
        28: [TYPE_POISON, TYPE_DARK],
        # TRAINER_CLASS_CHANNELER = 42
        42: [TYPE_GHOST, TYPE_PSYCHIC],
        # TRAINER_CLASS_SWIMMER_F = 45
        45: [TYPE_WATER],
        # TRAINER_CLASS_PSYCHIC_F = 46
        46: [TYPE_PSYCHIC],
    }

def GetTrainerCategoryForLegendaries(trainer_class, trainer_id):
    """Determina a categoria de um treinador para controle de lendários"""

    # Boss trainers (Gym Leaders, Elite Four, Champion)
    boss_trainer_ids = [
        # Gym Leaders
        0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2, 0x1A4, 0x1A3, 0x15E,
        # Elite Four Round 1 e Round 2
        0x19A, 0x19B, 0x19C, 0x19D, 0x2DF, 0x2E0, 0x2E1, 0x2E2,
        # Giovanni
        0x15C, 0x15D
    ]

    # Important trainers (Rivals)
    important_trainer_ids = [
        0x148, 0x146, 0x147, 0x14B, 0x149, 0x14A, 0x14E, 0x14C, 0x14D,
        0x1A9, 0x1A7, 0x1A8, 0x1AF, 0x1AD, 0x1AE, 0x1B2, 0x1B0, 0x1B1,
        0x1B5, 0x1B3, 0x1B4, 0x1B8, 0x1B6, 0x1B7, 0x2E5, 0x2E3, 0x2E4
    ]

    if trainer_id in boss_trainer_ids:
        return "BOSS"
    elif trainer_id in important_trainer_ids:
        return "IMPORTANT"
    else:
        return "REGULAR"

def IsTrainerPokemonLegendary(species):
    """Verifica se um Pokémon é lendário usando Project Species IDs corretos"""
    # CORRECTED: Using Project Species IDs instead of National Dex IDs
    # This list was generated by fix_national_dex_to_project_ids.py
    legendary_pokemon = {
        # Gen I-II Legendaries
        151, 243, 244, 245, 249, 250, 251, 883, 1221, 1222,
        1223,

        # Gen III Legendaries
        401, 402, 403, 907, 908, 909, 910, 911, 1042,

        # Gen IV Legendaries
        533, 534, 535, 538, 539, 542, 543, 544, 706, 718,
        719, 834, 919, 920,

        # Gen V Legendaries
        547, 691, 692, 693, 697, 746, 750, 753, 754, 755,
        756, 757,

        # Gen VI Legendaries
        825, 829, 830, 918, 1101,

        # Gen VII Legendaries
        1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011,
        1012, 1013, 1014, 1015, 1016, 1019, 1073, 1078, 1081, 1083,
        1273,

        # Gen VIII Legendaries
        1183, 1186, 1187, 1188, 1189, 1205, 1206, 1207, 1209, 1211,
        1293,

        # Gen IX+ Legendaries (including Paradox)
        1075, 1182, 1391, 1392, 1393, 1394, 1395, 1398,
        1399, 1400, 1405, 1406, 1407, 1408,
        # NOTE: 1396 (GIMMIGHOUL) and 1397 (GIMMIGHOUL_ROAMING) are NOT legendary - removed from list

    }

    return species in legendary_pokemon

def IsTrainerPokemonMythical(species):
    """Verifica se um Pokémon é mítico usando IDs corretos do projeto expandido"""
    # Lista completa de Pokémon míticos baseada em species.h
    mythical_pokemon = [
        # Generation I (1 Mythical)
        0x97,  # Mew

        # Generation II (1 Mythical)
        0xFB,  # Celebi

        # Generation III (2 Mythical)
        0x199, 0x19A,  # Jirachi, Deoxys

        # Generation IV (4 Mythical)
        0x21E, 0x21F, 0x220, 0x221,  # Phione, Manaphy, Darkrai, Shaymin

        # Generation V (3 Mythical)
        0x223, 0x2BC, 0x2BD, 0x2BE,  # Victini, Keldeo, Meloetta, Genesect

        # Generation VI (3 Mythical)
        0x33B, 0x33C, 0x33E,  # Diancie, Hoopa, Volcanion

        # Generation VII (4 Mythical)
        0x3FA, 0x3FB, 0x432, 0x436,  # Magearna, Marshadow, Poipole, Zeraora
        0x43B, 0x43C,  # Meltan, Melmetal

        # Generation VIII (2 Mythical)
        0x4A1, 0x4B9,  # Zarude, Zarude-Dada

        # Generation IX (1 Mythical)
        0x59F,  # Pecharunt

        # Alternate Forms (Mythical)
        0x410, 0x411, 0x412,  # Deoxys-Attack, Deoxys-Defense, Deoxys-Speed
        0x2CF,  # Shaymin-Sky
        0x33D,  # Hoopa-Unbound
        0x431,  # Magearna-Pokeball
        0x2EA, 0x2EB, 0x2EC, 0x2ED, 0x2EE,  # Meloetta-Pirouette, Genesect forms
    ]

    return species in mythical_pokemon

def GetPokemonAltFormes(base_species):
    """Retorna lista de formes alternativas para um Pokémon base (baseado no projeto)"""
    # Baseado em src/Evolution Table.c e include/base_stats.h
    alt_formes = {
        # Mega Evolutions (baseado em Evolution Table.c)
        3: [1296],    # Venusaur -> Venusaur Mega
        6: [1297, 1298],  # Charizard -> Charizard Mega X, Y
        9: [1299],    # Blastoise -> Blastoise Mega
        15: [1300],   # Beedrill -> Beedrill Mega
        18: [1301],   # Pidgeot -> Pidgeot Mega
        25: [1302],   # Pikachu -> Pikachu Giga
        65: [1303],   # Alakazam -> Alakazam Mega
        68: [1304],   # Machamp -> Machamp Giga
        94: [1305],   # Gengar -> Gengar Mega, Gengar Giga
        115: [1306],  # Kangaskhan -> Kangaskhan Mega
        127: [1307],  # Pinsir -> Pinsir Mega
        130: [1308],  # Gyarados -> Gyarados Mega
        142: [1309],  # Aerodactyl -> Aerodactyl Mega
        150: [1310],  # Mewtwo -> Mewtwo Mega X, Y

        # Regional Forms (baseado em Evolution Table.c)
        19: [605],    # Rattata -> Rattata Alolan
        20: [606],    # Raticate -> Raticate Alolan
        26: [607],    # Raichu -> Raichu Alolan
        27: [608],    # Sandshrew -> Sandshrew Alolan
        28: [609],    # Sandslash -> Sandslash Alolan
        37: [610],    # Vulpix -> Vulpix Alolan
        38: [611],    # Ninetales -> Ninetales Alolan
        50: [612],    # Diglett -> Diglett Alolan
        51: [613],    # Dugtrio -> Dugtrio Alolan
        52: [614],    # Meowth -> Meowth Alolan, Galarian
        53: [615],    # Persian -> Persian Alolan
        74: [616],    # Geodude -> Geodude Alolan
        75: [617],    # Graveler -> Graveler Alolan
        76: [618],    # Golem -> Golem Alolan
        88: [619],    # Grimer -> Grimer Alolan
        89: [620],    # Muk -> Muk Alolan
        103: [621],   # Exeggutor -> Exeggutor Alolan
        105: [622],   # Marowak -> Marowak Alolan

        # Gigantamax Forms (baseado em Evolution Table.c)
        # Já incluídos acima com Megas
    }

    return alt_formes.get(base_species, [])

def GetPokemonEvolutions(rom, species):
    """Lê as evoluções de um Pokémon da Evolution Table (baseado em src/Evolution Table.c)"""
    # Estrutura: struct Evolution { u16 method; u16 param; u16 targetSpecies; u16 unknown; }
    # Cada Pokémon tem EVOS_PER_MON (5) entradas de 8 bytes cada

    # Offset da Evolution Table (precisa ser determinado dinamicamente)
    # Por enquanto, retornar evoluções hardcoded baseadas no Evolution Table.c

    evolutions = {
        # Gen 1 evolutions (baseado em Evolution Table.c)
        1: [2],      # Bulbasaur -> Ivysaur
        2: [3],      # Ivysaur -> Venusaur
        4: [5],      # Charmander -> Charmeleon
        5: [6],      # Charmeleon -> Charizard
        7: [8],      # Squirtle -> Wartortle
        8: [9],      # Wartortle -> Blastoise
        10: [11],    # Caterpie -> Metapod
        11: [12],    # Metapod -> Butterfree
        13: [14],    # Weedle -> Kakuna
        14: [15],    # Kakuna -> Beedrill
        16: [17],    # Pidgey -> Pidgeotto
        17: [18],    # Pidgeotto -> Pidgeot
        19: [20],    # Rattata -> Raticate
        21: [22],    # Spearow -> Fearow
        23: [24],    # Ekans -> Arbok
        25: [26],    # Pikachu -> Raichu (via Thunder Stone)
        27: [28],    # Sandshrew -> Sandslash
        29: [30],    # Nidoran♀ -> Nidorina
        30: [31],    # Nidorina -> Nidoqueen (via Moon Stone)
        32: [33],    # Nidoran♂ -> Nidorino
        33: [34],    # Nidorino -> Nidoking (via Moon Stone)
        35: [36],    # Clefairy -> Clefable (via Moon Stone)
        37: [38],    # Vulpix -> Ninetales (via Fire Stone)
        39: [40],    # Jigglypuff -> Wigglytuff (via Moon Stone)
        41: [42],    # Zubat -> Golbat
        43: [44],    # Oddish -> Gloom
        44: [45, 182],  # Gloom -> Vileplume (Leaf Stone) or Bellossom (Sun Stone)
        46: [47],    # Paras -> Parasect
        48: [49],    # Venonat -> Venomoth
        50: [51],    # Diglett -> Dugtrio
        52: [53],    # Meowth -> Persian
        54: [55],    # Psyduck -> Golduck
        56: [57],    # Mankey -> Primeape
        58: [59],    # Growlithe -> Arcanine (via Fire Stone)
        60: [61],    # Poliwag -> Poliwhirl
        61: [62, 186],  # Poliwhirl -> Poliwrath (Water Stone) or Politoed (trade King's Rock)
        63: [64],    # Abra -> Kadabra
        64: [65],    # Kadabra -> Alakazam (trade)
        66: [67],    # Machop -> Machoke
        67: [68],    # Machoke -> Machamp (trade)
        69: [70],    # Bellsprout -> Weepinbell
        70: [71],    # Weepinbell -> Victreebel (via Leaf Stone)
        72: [73],    # Tentacool -> Tentacruel
        74: [75],    # Geodude -> Graveler
        75: [76],    # Graveler -> Golem (trade)
        77: [78],    # Ponyta -> Rapidash
        79: [80],    # Slowpoke -> Slowbro
        79: [199],   # Slowpoke -> Slowking (trade King's Rock)
        81: [82],    # Magnemite -> Magneton
        84: [85],    # Doduo -> Dodrio
        86: [87],    # Seel -> Dewgong
        88: [89],    # Grimer -> Muk
        90: [91],    # Shellder -> Cloyster (via Water Stone)
        92: [93],    # Gastly -> Haunter
        93: [94],    # Haunter -> Gengar (trade)
        95: [208],   # Onix -> Steelix (trade Metal Coat)
        96: [97],    # Drowzee -> Hypno
        98: [99],    # Krabby -> Kingler
        100: [101],  # Voltorb -> Electrode
        102: [103],  # Exeggcute -> Exeggutor (via Leaf Stone)
        104: [105],  # Cubone -> Marowak
        108: [463],  # Lickitung -> Lickilicky (knows Rollout)
        109: [110],  # Koffing -> Weezing
        111: [112],  # Rhyhorn -> Rhydon
        112: [464],  # Rhydon -> Rhyperior (trade Protector)
        114: [465],  # Tangela -> Tangrowth (knows Ancient Power)
        116: [117],  # Horsea -> Seadra
        117: [230],  # Seadra -> Kingdra (trade Dragon Scale)
        118: [119],  # Goldeen -> Seaking
        120: [121],  # Staryu -> Starmie (via Water Stone)
        123: [212],  # Scyther -> Scizor (trade Metal Coat)
        125: [466],  # Electabuzz -> Electivire (trade Electirizer)
        126: [467],  # Magmar -> Magmortar (trade Magmarizer)
        129: [130],  # Magikarp -> Gyarados
        133: [134, 135, 136, 196, 197, 470, 471, 700],  # Eevee -> multiple evolutions
        138: [139],  # Omanyte -> Omastar
        140: [141],  # Kabuto -> Kabutops
        147: [148],  # Dratini -> Dragonair
        148: [149],  # Dragonair -> Dragonite

        # Gen 2+ evolutions continuariam aqui...
        # Por brevidade, incluindo apenas alguns exemplos
        152: [153],  # Chikorita -> Bayleef
        153: [154],  # Bayleef -> Meganium
        155: [156],  # Cyndaquil -> Quilava
        156: [157],  # Quilava -> Typhlosion
        158: [159],  # Totodile -> Croconaw
        159: [160],  # Croconaw -> Feraligatr
    }

    return evolutions.get(species, [])

def FullyEvolvePokemon(rom, species, trainer_index=0):
    """Evolui um Pokémon até sua forma final (baseado no .randomizer fullyEvolve)"""
    seen_mons = set()
    seen_mons.add(species)
    current_species = species

    # Continue evoluindo até não ter mais evoluções
    evolution_attempts = 0
    max_attempts = 10  # Prevenir loops infinitos

    while evolution_attempts < max_attempts:
        evolutions = GetPokemonEvolutions(rom, current_species)

        if not evolutions or current_species in seen_mons:
            break

        if len(evolutions) > 1:
            # Split evolution - escolher deterministicamente baseado no trainer
            evolution_index = trainer_index % len(evolutions)
            next_species = evolutions[evolution_index]
        else:
            # Evolução linear
            next_species = evolutions[0]

        if next_species in seen_mons:
            break

        seen_mons.add(next_species)
        current_species = next_species
        evolution_attempts += 1

    return current_species

def ApplyTrainerPokemonRandomizationWithProject(original_species, level, project_pokemon_data, trainer_class=None, debug=False, current_legendary_count=0, max_legendaries=0, trainer_id=None):
    """
    NOVA FUNÇÃO: Aplica randomização usando dados do projeto em vez de ROM
    Implementa a hierarquia de prioridades correta com dados do projeto
    """
    import random

    # Verificar se deve bloquear lendários
    block_legendaries = (current_legendary_count >= max_legendaries) if (max_legendaries is not None and max_legendaries > 0) else False

    # Configurações de randomização (ler do arquivo de configuração)
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        use_similar_strength = "TRAINER_USE_SIMILAR_STRENGTH TRUE" in content
        use_type_themes = "TRAINER_USE_TYPE_THEMES TRUE" in content
        allow_alt_formes = "TRAINER_ALLOW_ALT_FORMES TRUE" in content
        same_type_priority = "TRAINER_SAME_TYPE_PRIORITY TRUE" in content
        force_fully_evolved = "TRAINER_FORCE_FULLY_EVOLVED TRUE" in content

        # Ler nível mínimo para evolução forçada
        force_fully_evolved_level = 40  # Padrão
        import re
        level_match = re.search(r'TRAINER_FORCE_FULLY_EVOLVED_LEVEL\s+(\d+)', content)
        if level_match:
            force_fully_evolved_level = int(level_match.group(1))

        # Ler tolerância BST
        bst_tolerance = 10  # Padrão ±10%
        tolerance_match = re.search(r'DEFAULT_BST_TOLERANCE\s+(\d+)', content)
        if tolerance_match:
            bst_tolerance = int(tolerance_match.group(1))

    except:
        # Valores padrão se não conseguir ler configuração
        use_similar_strength = True
        use_type_themes = True
        allow_alt_formes = True
        same_type_priority = True
        force_fully_evolved = False
        force_fully_evolved_level = 40
        bst_tolerance = 10

    # PRIORIDADE 1: Same Type Priority (tipos exatos)
    if same_type_priority:
        candidates = FindSameTypePokemon(original_species, project_pokemon_data, None,
                                       block_legendaries, allow_alt_formes, use_similar_strength, bst_tolerance, debug)
        if candidates:
            selected = random.choice(candidates)
            # Aplicar Force Fully Evolved se configurado
            if force_fully_evolved and level >= force_fully_evolved_level:
                selected = FullyEvolvePokemon(rom, selected, trainer_class or 0)
            return selected

    # PRIORIDADE 2: Partial Type Matching (pelo menos um tipo em comum)
    candidates = FindPartialTypePokemon(original_species, project_pokemon_data,
                                      block_legendaries, allow_alt_formes, use_similar_strength, bst_tolerance, debug)
    if candidates:
        selected = random.choice(candidates)
        # Aplicar Force Fully Evolved se configurado
        if force_fully_evolved and level >= force_fully_evolved_level:
            selected = FullyEvolvePokemon(rom, selected, trainer_class or 0)
        return selected

    # PRIORIDADE 3: Type Themes (baseado na classe do trainer)
    if use_type_themes and trainer_class:
        trainer_type_themes = GetTrainerTypeThemes()
        if trainer_class in trainer_type_themes:
            theme_types = trainer_type_themes[trainer_class]
            candidates = FindTypeThemePokemon(original_species, project_pokemon_data, theme_types,
                                            block_legendaries, allow_alt_formes, use_similar_strength, bst_tolerance, debug)
            if candidates:
                selected = random.choice(candidates)
                # Aplicar Force Fully Evolved se configurado
                if force_fully_evolved and level >= force_fully_evolved_level:
                    selected = FullyEvolvePokemon(rom, selected, trainer_class or 0)
                return selected

    # PRIORIDADE 4: Similar Strength (apenas BST similar)
    candidates = FindSimilarStrengthPokemon(original_species, project_pokemon_data,
                                          block_legendaries, allow_alt_formes, False, 38, level, trainer_class, debug)
    if candidates != original_species:
        selected = candidates
        # Aplicar Force Fully Evolved se configurado
        if force_fully_evolved and level >= force_fully_evolved_level:
            selected = FullyEvolvePokemon(rom, selected, trainer_class or 0)
        return selected

    # FALLBACK: Retornar original se nada funcionou
    return original_species

def ApplyTrainerPokemonRandomization(rom, original_species, level, pokemon_stats_offset, trainer_class=None, debug=False, current_legendary_count=0, max_legendaries=0):
    """Aplica randomização específica para Pokémon de treinadores com filtros apropriados"""
    import random
    import struct

    # Type themes para trainer classes (baseado no projeto include/base_stats.h)
    # Usando as constantes corretas do projeto
    TYPE_NORMAL = 0x00
    TYPE_FIGHTING = 0x01
    TYPE_FLYING = 0x02
    TYPE_POISON = 0x03
    TYPE_GROUND = 0x04
    TYPE_ROCK = 0x05
    TYPE_BUG = 0x06
    TYPE_GHOST = 0x07
    TYPE_STEEL = 0x08
    TYPE_FIRE = 0x0a
    TYPE_WATER = 0x0b
    TYPE_GRASS = 0x0c
    TYPE_ELECTRIC = 0x0d
    TYPE_PSYCHIC = 0x0e
    TYPE_ICE = 0x0f
    TYPE_DRAGON = 0x10
    TYPE_DARK = 0x11
    TYPE_FAIRY = 0x17

    trainer_type_themes = {
        # CORREÇÃO: Mapeamento baseado nas constantes corretas de include/trainer_randomization.h
        # TRAINER_CLASS_YOUNGSTER = 1
        1: [],  # No theme
        # TRAINER_CLASS_BUG_CATCHER = 2
        2: [TYPE_BUG],  # Bug type
        # TRAINER_CLASS_LASS = 3
        3: [],  # No theme
        # TRAINER_CLASS_SAILOR = 4
        4: [TYPE_WATER, TYPE_FIGHTING],  # Water, Fighting types
        # TRAINER_CLASS_CAMPER = 5
        5: [],  # No theme
        # TRAINER_CLASS_PICNICKER = 6
        6: [],  # No theme
        # TRAINER_CLASS_POKEMANIAC = 7
        7: [],  # No theme
        # TRAINER_CLASS_SUPER_NERD = 8
        8: [TYPE_ELECTRIC, TYPE_PSYCHIC],  # Electric, Psychic types
        # TRAINER_CLASS_HIKER = 9
        9: [TYPE_ROCK, TYPE_GROUND],  # Rock, Ground types
        # TRAINER_CLASS_BIKER = 10
        10: [TYPE_POISON],   # Poison type
        # TRAINER_CLASS_BURGLAR = 11
        11: [TYPE_FIRE, TYPE_POISON],  # Fire, Poison types
        # TRAINER_CLASS_ENGINEER = 12
        12: [],  # No theme
        # TRAINER_CLASS_JUGGLER = 13
        13: [TYPE_PSYCHIC, TYPE_POISON],  # Psychic, Poison types
        # TRAINER_CLASS_FISHERMAN = 14
        14: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_SWIMMER_M = 15
        15: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_CUE_BALL = 16
        16: [],  # No theme
        # TRAINER_CLASS_GAMBLER = 17
        17: [],  # No theme
        # TRAINER_CLASS_BEAUTY = 18
        18: [],  # No theme
        # TRAINER_CLASS_PSYCHIC_M = 19
        19: [TYPE_PSYCHIC],  # Psychic type
        # TRAINER_CLASS_ROCKER = 20
        20: [TYPE_ELECTRIC],  # Electric type
        # TRAINER_CLASS_JUGGLER_2 = 21
        21: [TYPE_PSYCHIC, TYPE_POISON],  # Psychic, Poison types
        # TRAINER_CLASS_TAMER = 22
        22: [TYPE_NORMAL],  # Normal type
        # TRAINER_CLASS_BIRD_KEEPER = 23
        23: [TYPE_FLYING, TYPE_NORMAL],  # Flying, Normal types
        # TRAINER_CLASS_BLACKBELT = 24
        24: [TYPE_FIGHTING],  # Fighting type
        # TRAINER_CLASS_RIVAL1 = 25 (Boss - não randomizar)
        25: [],  # Boss trainer
        # TRAINER_CLASS_SCIENTIST = 26
        26: [TYPE_ELECTRIC, TYPE_POISON],  # Electric, Poison types
        # TRAINER_CLASS_GIOVANNI = 27 (Boss - TESTE: Ground/Rock theme)
        27: [TYPE_GROUND, TYPE_ROCK],  # Ground, Rock types (Onix, Rhyhorn theme)
        # TRAINER_CLASS_ROCKET = 28
        28: [TYPE_POISON, TYPE_DARK],  # Poison, Dark types
        # TRAINER_CLASS_COOLTRAINER_M = 29 (Boss - não randomizar)
        29: [],  # Boss trainer
        # TRAINER_CLASS_COOLTRAINER_F = 30 (Boss - não randomizar)
        30: [],  # Boss trainer
        # Classes 31-48 são Boss trainers (Gym Leaders, Elite Four, etc.)
        # TRAINER_CLASS_CHANNELER = 42
        42: [TYPE_GHOST, TYPE_PSYCHIC],  # Ghost, Psychic types
        # TRAINER_CLASS_SWIMMER_F = 45
        45: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_PSYCHIC_F = 46
        46: [TYPE_PSYCHIC],  # Psychic type
    }

    # Verificar configuração
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Controle de lendários: usar limite específico se disponível, senão usar bloqueio global
        block_legendaries_global = "TRAINER_BLOCK_LEGENDARIES TRUE" in content
        block_legendaries = block_legendaries_global or (current_legendary_count >= max_legendaries)

        use_similar_strength = "TRAINER_USE_SIMILAR_STRENGTH TRUE" in content
        use_type_themes = "TRAINER_USE_TYPE_THEMES TRUE" in content
        allow_alt_formes = "TRAINER_ALLOW_ALT_FORMES TRUE" in content
        force_fully_evolved = "TRAINER_FORCE_FULLY_EVOLVED TRUE" in content
        same_type_priority = "TRAINER_SAME_TYPE_PRIORITY TRUE" in content

        # Extrair força do tema (padrão 85)
        theme_strength = 85
        if "TRAINER_TYPE_THEME_STRENGTH" in content:
            import re
            match = re.search(r'TRAINER_TYPE_THEME_STRENGTH\s+(\d+)', content)
            if match:
                theme_strength = int(match.group(1))

        # Extrair nível mínimo para evolução forçada (padrão 38)
        force_fully_evolved_level = 38
        if "TRAINER_FORCE_FULLY_EVOLVED_LEVEL" in content:
            import re
            match = re.search(r'TRAINER_FORCE_FULLY_EVOLVED_LEVEL\s+(\d+)', content)
            if match:
                force_fully_evolved_level = int(match.group(1))
    except:
        block_legendaries = True  # Padrão seguro
        use_similar_strength = True
        use_type_themes = True
        allow_alt_formes = False
        force_fully_evolved = False
        same_type_priority = True
        theme_strength = 85
        force_fully_evolved_level = 38

    # CARREGAR DADOS DO PROJETO
    project_pokemon_data = LoadProjectPokemonDatabase()

    # PRIORIDADE 1 (ABSOLUTA): Same Type Priority - tipos exatos do Pokémon original
    if same_type_priority:
        # Primeiro: buscar candidatos com tipos exatos (sem filtro de Type Themes)
        same_type_candidates = FindSameTypePokemon(
            original_species, project_pokemon_data,
            None, block_legendaries, allow_alt_formes, use_similar_strength, debug
        )

        if same_type_candidates:
            selected_species = random.choice(same_type_candidates)

            # Aplicar forceFullyEvolved se configurado
            if force_fully_evolved and level >= force_fully_evolved_level:
                evolved_species = FullyEvolvePokemon(rom, selected_species, trainer_class or 0)
                return evolved_species

            return selected_species

    # PRIORIDADE 2 (FALLBACK): Partial Type Matching - pelo menos um tipo em comum
    partial_type_candidates = FindPartialTypePokemon(
        original_species, project_pokemon_data,
        block_legendaries, allow_alt_formes, use_similar_strength, debug
    )

    if partial_type_candidates:
        selected_species = random.choice(partial_type_candidates)

        # Aplicar forceFullyEvolved se configurado
        if force_fully_evolved and level >= force_fully_evolved_level:
            evolved_species = FullyEvolvePokemon(rom, selected_species, trainer_class or 0)
            return evolved_species

        return selected_species

    # PRIORIDADE 3 (ÚLTIMO RECURSO): Type Themes - apenas se não há matches de tipo
    # Determinar tipos permitidos baseado no type theme (apenas como último recurso)
    allowed_types = None
    if use_type_themes and trainer_class and trainer_class in trainer_type_themes:
        theme_types = trainer_type_themes[trainer_class]
        if theme_types:  # Se tem tema definido
            # Aplicar tema com probabilidade baseada na força
            roll = random.randint(1, 100)

            if roll <= theme_strength:
                # FORÇAR o tipo do treinador (como último recurso)
                allowed_types = theme_types

    # Se Type Themes foi aplicado, buscar candidatos com esses tipos
    if allowed_types:
        theme_candidates = FindTypeThemePokemon(
            original_species, project_pokemon_data,
            allowed_types, block_legendaries, allow_alt_formes, use_similar_strength, debug
        )

        if theme_candidates:
            selected_species = random.choice(theme_candidates)

            # Aplicar forceFullyEvolved se configurado
            if force_fully_evolved and level >= force_fully_evolved_level:
                evolved_species = FullyEvolvePokemon(rom, selected_species, trainer_class or 0)
                return evolved_species

            return selected_species

    # FALLBACK FINAL: Similar Strength sem restrições de tipo
    return FindSimilarStrengthPokemon(
        original_species, project_pokemon_data,
        block_legendaries, allow_alt_formes, force_fully_evolved,
        force_fully_evolved_level, level, trainer_class, debug
    )

def FindSameTypePokemon(original_species, project_pokemon_data, allowed_types=None,
                       block_legendaries=True, allow_alt_formes=True, use_similar_strength=True, bst_tolerance=10, debug=False):
    """
    PRIORIDADE 1: Encontra Pokémon com EXATAMENTE os mesmos tipos que o original
    REFATORADO: Usa dados do projeto em vez de ROM

    Exemplo: Onix (Rock/Ground) -> busca apenas outros Rock/Ground
    """

    # Obter dados do Pokémon original do PROJETO
    original_data = project_pokemon_data.get(original_species)
    if not original_data:
        return []

    original_type1 = original_data['type1']
    original_type2 = original_data['type2']
    original_bst = original_data['bst'] if use_similar_strength else 0

    if original_type1 == 0 and original_type2 == 0:
        return []

    # Determinar range de Pokémon baseado em allowAltFormes
    if allow_alt_formes:
        pokemon_range = range(1, 1441)
    else:
        pokemon_range = range(1, 1026)

    exact_type_candidates = []
    checked_count = 0
    legendary_blocks = 0
    bst_blocks = 0

    for species in pokemon_range:
        if species == original_species:
            continue

        checked_count += 1

        # Bloquear lendários se configurado
        if block_legendaries and IsTrainerPokemonLegendary(species):
            legendary_blocks += 1
            continue

        # Obter dados do candidato do PROJETO
        candidate_data = project_pokemon_data.get(species)
        if not candidate_data:
            continue

        candidate_type1 = candidate_data['type1']
        candidate_type2 = candidate_data['type2']

        if candidate_type1 == 0:
            continue

        # Verificar se tem EXATAMENTE os mesmos tipos (em qualquer ordem)
        exact_match = ((candidate_type1 == original_type1 and candidate_type2 == original_type2) or
                      (candidate_type1 == original_type2 and candidate_type2 == original_type1))

        if not exact_match:
            continue

        # Verificar Similar Strength (FILTRO SECUNDÁRIO)
        if use_similar_strength and original_bst > 0:
            candidate_bst = candidate_data['bst']
            # Calcular tolerância baseada na porcentagem configurada
            tolerance_value = int(original_bst * bst_tolerance / 100)
            if candidate_bst == 0 or abs(candidate_bst - original_bst) > tolerance_value:
                bst_blocks += 1
                continue

        exact_type_candidates.append(species)

    return exact_type_candidates

def FindPartialTypePokemon(original_species, project_pokemon_data,
                          block_legendaries=True, allow_alt_formes=True, use_similar_strength=True, bst_tolerance=10, debug=False):
    """
    PRIORIDADE 2: Encontra Pokémon que compartilham PELO MENOS UM tipo com o original
    REFATORADO: Usa dados do projeto em vez de ROM

    Exemplo: Onix (Rock/Ground) -> busca Rock/qualquer ou qualquer/Ground
    """
    import random

    # Obter dados do Pokémon original do PROJETO
    original_data = project_pokemon_data.get(original_species)
    if not original_data:
        return []

    original_type1 = original_data['type1']
    original_type2 = original_data['type2']
    original_bst = original_data['bst'] if use_similar_strength else 0

    if original_type1 == 0 and original_type2 == 0:
        return []

    # Determinar range de Pokémon baseado em allowAltFormes
    if allow_alt_formes:
        pokemon_range = range(1, 1441)
    else:
        pokemon_range = range(1, 1026)

    partial_type_candidates = []
    checked_count = 0
    legendary_blocks = 0
    bst_blocks = 0

    for species in pokemon_range:
        if species == original_species:
            continue

        checked_count += 1

        # Bloquear lendários se configurado
        if block_legendaries and IsTrainerPokemonLegendary(species):
            legendary_blocks += 1
            continue

        # Obter dados do candidato do PROJETO
        candidate_data = project_pokemon_data.get(species)
        if not candidate_data:
            continue

        candidate_type1 = candidate_data['type1']
        candidate_type2 = candidate_data['type2']

        if candidate_type1 == 0:
            continue

        # Verificar se compartilha PELO MENOS UM tipo (mas não ambos - isso seria prioridade 1)
        shares_one_type = (candidate_type1 == original_type1 or candidate_type1 == original_type2 or
                          candidate_type2 == original_type1 or candidate_type2 == original_type2)

        # Excluir matches exatos (esses são prioridade 1)
        exact_match = ((candidate_type1 == original_type1 and candidate_type2 == original_type2) or
                      (candidate_type1 == original_type2 and candidate_type2 == original_type1))

        if not shares_one_type or exact_match:
            continue

        # Verificar Similar Strength (FILTRO SECUNDÁRIO)
        if use_similar_strength and original_bst > 0:
            candidate_bst = candidate_data['bst']
            # Calcular tolerância baseada na porcentagem configurada
            tolerance_value = int(original_bst * bst_tolerance / 100)
            if candidate_bst == 0 or abs(candidate_bst - original_bst) > tolerance_value:
                bst_blocks += 1
                continue

        partial_type_candidates.append(species)

    return partial_type_candidates

def FindTypeThemePokemon(original_species, project_pokemon_data, allowed_types,
                        block_legendaries=True, allow_alt_formes=True, use_similar_strength=True, bst_tolerance=10, debug=False):
    """
    PRIORIDADE 3: Encontra Pokémon que atendem aos Type Themes (último recurso)
    REFATORADO: Usa dados do projeto em vez de ROM

    Exemplo: Giovanni (Ground/Rock theme) -> busca qualquer Ground ou Rock
    """
    import random

    # Obter BST do original para Similar Strength do PROJETO
    original_data = project_pokemon_data.get(original_species)
    original_bst = original_data['bst'] if (use_similar_strength and original_data) else 0

    # Determinar range de Pokémon baseado em allowAltFormes
    if allow_alt_formes:
        pokemon_range = range(1, 1441)
    else:
        pokemon_range = range(1, 1026)

    theme_candidates = []
    checked_count = 0
    legendary_blocks = 0
    bst_blocks = 0

    for species in pokemon_range:
        if species == original_species:
            continue

        checked_count += 1

        # Bloquear lendários se configurado
        if block_legendaries and IsTrainerPokemonLegendary(species):
            legendary_blocks += 1
            continue

        # Obter dados do candidato do PROJETO
        candidate_data = project_pokemon_data.get(species)
        if not candidate_data:
            continue

        candidate_type1 = candidate_data['type1']
        candidate_type2 = candidate_data['type2']

        if candidate_type1 == 0:
            continue

        # Verificar se atende ao Type Theme
        matches_theme = (candidate_type1 in allowed_types or candidate_type2 in allowed_types)
        if not matches_theme:
            continue

        # Verificar Similar Strength (FILTRO SECUNDÁRIO)
        if use_similar_strength and original_bst > 0:
            candidate_bst = candidate_data['bst']
            # Calcular tolerância baseada na porcentagem configurada
            tolerance_value = int(original_bst * bst_tolerance / 100)
            if candidate_bst == 0 or abs(candidate_bst - original_bst) > tolerance_value:
                bst_blocks += 1
                continue

        theme_candidates.append(species)

    return theme_candidates

def FindSimilarStrengthPokemon(original_species, project_pokemon_data,
                              block_legendaries=True, allow_alt_formes=True, force_fully_evolved=False,
                              force_fully_evolved_level=38, level=1, trainer_class=0, debug=False):
    """
    FALLBACK FINAL: Encontra Pokémon apenas por Similar Strength (sem restrições de tipo)
    REFATORADO: Usa dados do projeto em vez de ROM
    """
    import random

    # Obter BST do original do PROJETO
    original_data = project_pokemon_data.get(original_species)
    if not original_data:
        return original_species

    original_bst = original_data['bst']
    if original_bst == 0:
        return original_species

    # Determinar range de Pokémon baseado em allowAltFormes
    if allow_alt_formes:
        pokemon_range = range(1, 1441)
    else:
        pokemon_range = range(1, 1026)

    candidates = []
    checked_count = 0
    legendary_blocks = 0

    for species in pokemon_range:
        if species == original_species:
            continue

        checked_count += 1

        # Bloquear lendários se configurado
        if block_legendaries and IsTrainerPokemonLegendary(species):
            legendary_blocks += 1
            continue

        # Obter dados do candidato do PROJETO
        candidate_data = project_pokemon_data.get(species)
        if not candidate_data:
            continue

        # Verificar Similar Strength
        candidate_bst = candidate_data['bst']
        if candidate_bst == 0 or abs(candidate_bst - original_bst) > 50:
            continue

        candidates.append(species)

    if candidates:
        selected_species = random.choice(candidates)

        # Aplicar forceFullyEvolved se configurado
        if force_fully_evolved and level >= force_fully_evolved_level:
            # NOTA: FullyEvolvePokemon ainda usa ROM - manter por enquanto
            evolved_species = FullyEvolvePokemon(None, selected_species, trainer_class)
            return evolved_species

        return selected_species

    # Último recurso: randomização simples

    max_attempts = 100
    for _ in range(max_attempts):
        candidate = random.randint(1, 1440)
        if block_legendaries and IsTrainerPokemonLegendary(candidate):
            continue
        return candidate

    # Se tudo falhou, retornar original
    return original_species

# OPTIMIZED: Combined Pokemon data cache
_pokemon_data_cache = {}

def GetPokemonDataFromOffset(rom, species, pokemon_stats_offset):
    """Obtém BST e tipos de um Pokémon com cache otimizado"""
    global _pokemon_data_cache

    if species == 0 or species > 1440:
        return 0, (0, 0)

    # Check cache first
    if species in _pokemon_data_cache:
        return _pokemon_data_cache[species]

    try:
        # Single ROM operation
        stats_entry_size = 28
        # CORREÇÃO: Species é 1-based, então usar (species - 1) para offset correto
        entry_offset = pokemon_stats_offset + ((species - 1) * stats_entry_size)

        rom.seek(entry_offset)
        stats_data = rom.read(28)

        if len(stats_data) < 28:
            return 0, (0, 0)

        # Calculate BST
        bst = sum(stats_data[0:6])  # HP + ATK + DEF + SPE + SPA + SPD

        # Get types
        types = (stats_data[6], stats_data[7])

        # Cache result
        _pokemon_data_cache[species] = (bst, types)
        return bst, types

    except:
        return 0, (0, 0)

def GetPokemonBSTFromOffset(rom, species, pokemon_stats_offset):
    """Obtém o BST de um Pokémon (wrapper otimizado)"""
    bst, _ = GetPokemonDataFromOffset(rom, species, pokemon_stats_offset)
    return bst

def GetPokemonTypesFromOffset(rom, species, pokemon_stats_offset):
    """Obtém os tipos de um Pokémon (números de tipos do projeto customizado)"""
    _, types = GetPokemonDataFromOffset(rom, species, pokemon_stats_offset)
    return types

def GetPokemonStandardTypesFromOffset(rom, species, pokemon_stats_offset):
    """Obtém os tipos de um Pokémon convertidos para padrão Gen 3 Fire Red (para comparações)"""
    _, project_types = GetPokemonDataFromOffset(rom, species, pokemon_stats_offset)

    # Converter tipos do projeto para tipos padrão Gen 3 Fire Red
    standard_type1 = ConvertProjectTypeToStandardType(project_types[0])
    standard_type2 = ConvertProjectTypeToStandardType(project_types[1])

    return (standard_type1, standard_type2)

def GetPokemonTypeNamesFromOffset(rom, species, pokemon_stats_offset):
    """Obtém os nomes dos tipos de um Pokémon (usando tabela do projeto)"""
    _, project_types = GetPokemonDataFromOffset(rom, species, pokemon_stats_offset)

    # Converter tipos do projeto para nomes
    type1_name = ConvertGen3TypeToName(project_types[0])
    type2_name = ConvertGen3TypeToName(project_types[1])

    return (type1_name, type2_name)

def ConvertGen3TypeToName(type_byte):
    """Converte byte de tipo da ROM para nome do tipo (baseado em include/base_stats.h)"""
    # Tabela de tipos do projeto atual (include/base_stats.h)
    project_type_table = {
        0x00: "Normal",     # TYPE_NORMAL
        0x01: "Fighting",   # TYPE_FIGHTING
        0x02: "Flying",     # TYPE_FLYING
        0x03: "Poison",     # TYPE_POISON
        0x04: "Ground",     # TYPE_GROUND
        0x05: "Rock",       # TYPE_ROCK
        0x06: "Bug",        # TYPE_BUG
        0x07: "Ghost",      # TYPE_GHOST
        0x08: "Steel",      # TYPE_STEEL
        0x09: "Mystery",    # TYPE_MYSTERY
        0x0A: "Fire",       # TYPE_FIRE
        0x0B: "Water",      # TYPE_WATER
        0x0C: "Grass",      # TYPE_GRASS
        0x0D: "Electric",   # TYPE_ELECTRIC
        0x0E: "Psychic",    # TYPE_PSYCHIC
        0x0F: "Ice",        # TYPE_ICE
        0x10: "Dragon",     # TYPE_DRAGON
        0x11: "Dark",       # TYPE_DARK
        0x13: "Roostless",  # TYPE_ROOSTLESS
        0x14: "Blank",      # TYPE_BLANK
        0x17: "Fairy",      # TYPE_FAIRY
    }

    return project_type_table.get(type_byte, f"Unknown({type_byte:02X})")

def ConvertProjectTypeToStandardType(project_type_byte):
    """Converte tipo do projeto customizado para tipo padrão Gen 3 Fire Red"""
    # Mapeamento: Projeto Customizado → Gen 3 Fire Red Padrão
    project_to_standard = {
        0x00: 0x00,  # Normal → Normal
        0x01: 0x01,  # Fighting → Fighting
        0x02: 0x02,  # Flying → Flying
        0x03: 0x03,  # Poison → Poison
        0x04: 0x04,  # Ground → Ground
        0x05: 0x05,  # Rock → Rock
        0x06: 0x06,  # Bug → Bug
        0x07: 0x07,  # Ghost → Ghost
        0x08: 0x08,  # Steel → Steel
        0x09: 0x09,  # Mystery → ??? (Gen 3 padrão)
        0x0A: 0x0A,  # Fire → Fire
        0x0B: 0x0B,  # Water → Water
        0x0C: 0x0C,  # Grass → Grass
        0x0D: 0x0D,  # Electric → Electric
        0x0E: 0x0E,  # Psychic → Psychic
        0x0F: 0x0F,  # Ice → Ice
        0x10: 0x10,  # Dragon → Dragon
        0x11: 0x11,  # Dark → Dark
        0x13: 0x02,  # Roostless → Flying (mapeamento aproximado)
        0x14: 0x00,  # Blank → Normal (mapeamento aproximado)
        0x17: 0x00,  # Fairy → Normal (Gen 3 não tem Fairy)
    }

    return project_to_standard.get(project_type_byte, 0x00)  # Default: Normal

def ConvertStandardTypeToProjectType(standard_type_byte):
    """Converte tipo padrão Gen 3 Fire Red para tipo do projeto customizado"""
    # Mapeamento: Gen 3 Fire Red Padrão → Projeto Customizado
    standard_to_project = {
        0x00: 0x00,  # Normal → Normal
        0x01: 0x01,  # Fighting → Fighting
        0x02: 0x02,  # Flying → Flying
        0x03: 0x03,  # Poison → Poison
        0x04: 0x04,  # Ground → Ground
        0x05: 0x05,  # Rock → Rock
        0x06: 0x06,  # Bug → Bug
        0x07: 0x07,  # Ghost → Ghost
        0x08: 0x08,  # Steel → Steel
        0x09: 0x09,  # ??? → Mystery
        0x0A: 0x0A,  # Fire → Fire
        0x0B: 0x0B,  # Water → Water
        0x0C: 0x0C,  # Grass → Grass
        0x0D: 0x0D,  # Electric → Electric
        0x0E: 0x0E,  # Psychic → Psychic
        0x0F: 0x0F,  # Ice → Ice
        0x10: 0x10,  # Dragon → Dragon
        0x11: 0x11,  # Dark → Dark
    }

    return standard_to_project.get(standard_type_byte, 0x00)  # Default: Normal

def ExpandTrainerPartyWithOriginalData(original_rom, rom, trainer_offset, trainer_id, expansion_settings, pokemon_stats_offset):
    """Expande a equipe de um treinador usando ROM original para análise"""
    import struct

    try:
        # LER DADOS DO TREINADOR DA ROM ORIGINAL
        original_rom.seek(trainer_offset)
        trainer_data = bytearray(original_rom.read(40))
        if len(trainer_data) < 40:
            return 0

        # Continuar com a lógica de expansão usando dados originais
        return ExpandTrainerPartyCore(original_rom, rom, trainer_offset, trainer_id,
                                    trainer_data, expansion_settings, pokemon_stats_offset)

    except Exception as e:
        return 0

def ExpandTrainerPartyCore(original_rom, rom, trainer_offset, trainer_id, trainer_data, expansion_settings, pokemon_stats_offset):
    """Núcleo da expansão usando dados originais para análise"""
    import struct

    try:
        trainer_class = trainer_data[1]
        party_size = trainer_data[32]  # Offset 32 = party size (0x20)
        party_ptr = struct.unpack('<I', trainer_data[36:40])[0]  # Offset 36 = party pointer (0x24)

        # Determinar categoria do treinador
        category = GetTrainerCategory(trainer_class, trainer_id)

        # Determinar quantos Pokémon adicionar
        additional_count = 0
        if category == "REGULAR":
            additional_count = expansion_settings['regular']
        elif category == "IMPORTANT":
            additional_count = expansion_settings['important']
        elif category == "BOSS":
            additional_count = expansion_settings['boss']

        # GYM LEADERS SPECIFIC: Log all 8 gym leaders - CORREÇÃO: IDs em DECIMAL
        gym_leader_names = {
            414: "BROCK",      # Gym Leader 1 - Rock (0x19E)
            415: "MISTY",      # Gym Leader 2 - Water (0x19F)
            416: "LT_SURGE",   # Gym Leader 3 - Electric (0x1A0)
            417: "ERIKA",      # Gym Leader 4 - Grass (0x1A1)
            418: "KOGA",       # Gym Leader 5 - Poison (0x1A2)
            420: "BLAINE",     # Gym Leader 7 - Fire (0x1A3)
            419: "SABRINA",    # Gym Leader 6 - Psychic (0x1A4)
            350: "GIOVANNI",   # Gym Leader 8 - Ground (0x15E)
        }

        if trainer_id in gym_leader_names:
            leader_name = gym_leader_names[trainer_id]

        if additional_count == 0:
            
            return 0

        # NOVO: Tratamento para parties que já possuem 6 Pokémon
        if party_size >= 6:
            # Log apenas para Giovanni
            if trainer_id == 350:
                pass  # Giovanni com party cheia
            return HandleFullPartySubstitution(rom, trainer_offset, trainer_data, trainer_id, trainer_class,
                                             category, additional_count, pokemon_stats_offset)

        # Limitar ao máximo de 6 Pokémon
        max_additional = min(additional_count, 6 - party_size)
        if max_additional <= 0:
            return 0

        # Ler dados da equipe atual DA ROM ORIGINAL
        if not IsValidPointer(party_ptr):
            return 0

        party_offset = party_ptr - 0x08000000

        # Verificar se offset é válido
        original_rom.seek(0, 2)  # Ir para o final
        rom_size = original_rom.tell()
        if party_offset < 0 or party_offset >= rom_size:
            return 0

        original_rom.seek(party_offset)

        # Determinar tamanho da entrada baseado no party flags
        party_flags = trainer_data[0]
        entry_size = 8  # Base size
        if party_flags & 0x01:  # Custom moves
            entry_size += 8
        if party_flags & 0x02:  # Held items
            entry_size += 2

        # LER EQUIPE ATUAL DA ROM ORIGINAL
        current_party_data = bytearray(original_rom.read(party_size * entry_size))
        if len(current_party_data) < party_size * entry_size:
            return 0

        # Encontrar Pokémon de menor nível para usar como base
        lowest_level = 100
        base_pokemon_data = None

        for i in range(party_size):
            pokemon_offset = i * entry_size
            if pokemon_offset + entry_size <= len(current_party_data):
                level = current_party_data[pokemon_offset + 1]
                if level < lowest_level:
                    lowest_level = level
                    base_pokemon_data = current_party_data[pokemon_offset:pokemon_offset + entry_size]

        if base_pokemon_data is None:
            return 0

        # Salvar party size original para logs
        original_party_size = party_size

        # PASSO 1: ANALISAR PARTY ORIGINAL - Identificar tipos predominantes
        # USAR ROM ORIGINAL PARA ANÁLISE DE TIPOS
        party_type_analysis = AnalyzePartyTypesWithOriginalData(original_rom, current_party_data, party_size, entry_size, pokemon_stats_offset)

        # PASSO 2: ESCOLHER POKÉMON ADEQUADOS - Baseado nos tipos predominantes
        trainer_category = GetTrainerCategory(trainer_class, trainer_id)

        # GYM LEADERS SPECIFIC: Log type analysis for all gym leaders - CORREÇÃO: IDs em DECIMAL
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        if trainer_id in gym_leader_names:
            leader_name = gym_leader_names[trainer_id]
            primary = party_type_analysis.get('primary_type')
            secondary = party_type_analysis.get('secondary_type')
            type_counts = party_type_analysis.get('type_counts')
            allow_legendaries = ShouldAllowLegendariesInAdditionalSlot(trainer_category)

            primary_name = ConvertTypeNumberToName(primary) if primary else "None"
            secondary_name = ConvertTypeNumberToName(secondary) if secondary else "None"

        base_level = base_pokemon_data[1]  # Level do template

        additional_pokemon_list = SelectAdditionalPokemonWithAppropriateTypes(
            original_rom, party_type_analysis, trainer_class, trainer_id, trainer_category,
            max_additional, base_level, pokemon_stats_offset)

        if not additional_pokemon_list:
            return 0

        # PASSO 3: ADICIONAR À PARTY - Pokémon já randomizados com tipos corretos
        new_party_data = bytearray(current_party_data)

        # Coletar informações para log final dos gym leaders
        gym_leader_additions = []

        for i, selected_species in enumerate(additional_pokemon_list):
            # DEBUG: Log detalhado da inserção
            if trainer_id in gym_leader_names:
                print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Inserindo Pokémon {i+1}/{len(additional_pokemon_list)} - #{selected_species}")

            # CORREÇÃO: Inserir no final da party (não antes do último)
            insert_index = party_size

            # Criar novo Pokémon baseado no template, mas com species correto
            new_pokemon = bytearray(base_pokemon_data)

            # Atualizar species no novo Pokémon
            if 1 <= selected_species <= 1440:
                struct.pack_into('<H', new_pokemon, 4, selected_species)

                # Coletar informações para log final dos gym leaders
                if trainer_id in gym_leader_names:
                    is_legendary = IsTrainerPokemonLegendary(selected_species)
                    gym_leader_additions.append({
                        'species': selected_species,
                        'legendary': is_legendary
                    })
                    print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Species #{selected_species} válido, inserindo na posição {insert_index}")

            # Inserir na posição correta
            insert_offset = insert_index * entry_size
            new_party_data = (new_party_data[:insert_offset] +
                            new_pokemon +
                            new_party_data[insert_offset:])

            party_size += 1

            # DEBUG: Confirmar inserção
            if trainer_id in gym_leader_names:
                print(f"    🔍 DEBUG {gym_leader_names[trainer_id]}: Party size agora: {party_size}")

        # ESCREVER DADOS MODIFICADOS DE VOLTA NA ROM MODIFICADA
        rom.seek(party_offset)
        rom.write(new_party_data)

        # Atualizar party size no trainer NA ROM MODIFICADA
        rom.seek(trainer_offset)
        trainer_data[32] = party_size  # Offset correto para party size
        rom.write(trainer_data)

        # LOG FINAL SIMPLIFICADO PARA GYM LEADERS
        if trainer_id in gym_leader_names and gym_leader_additions:
            leader_name = gym_leader_names[trainer_id]
            additions_text = []
            for addition in gym_leader_additions:
                legendary_mark = " ⭐" if addition['legendary'] else ""
                additions_text.append(f"#{addition['species']}{legendary_mark}")

            print(f"🏆 {leader_name}: Added {', '.join(additions_text)}")

        return max_additional

    except Exception as e:
        print(f"    ❌ Trainer {trainer_id}: Exception during expansion: {e}")
        return 0

def ExpandTrainerParty(rom, trainer_offset, trainer_id, expansion_settings, pokemon_stats_offset):
    """Expande a equipe de um treinador baseado nas configurações"""
    import struct

    try:
        # Ler dados do treinador
        rom.seek(trainer_offset)
        trainer_data = bytearray(rom.read(40))
        if len(trainer_data) < 40:
            return 0

        # Fire Red trainer structure (24 bytes):
        # 0x00: party flags
        # 0x01: trainer class
        # 0x02: encounter music
        # 0x03: trainer pic
        # 0x04-0x0F: trainer name (12 bytes)
        # 0x10-0x17: items (4 x 2 bytes)
        # 0x18: double battle flag
        # 0x19: padding
        # 0x1A: padding
        # 0x1B: padding
        # 0x1C: AI flags (4 bytes)
        # 0x20: party size
        # 0x21-0x23: padding
        # 0x24: party pointer (4 bytes)

        trainer_class = trainer_data[1]
        party_size = trainer_data[32]  # Offset 32 = party size (0x20)
        party_ptr = struct.unpack('<I', trainer_data[36:40])[0]  # Offset 36 = party pointer (0x24)

        # Determinar categoria do treinador
        category = GetTrainerCategory(trainer_class, trainer_id)

        # Determinar quantos Pokémon adicionar
        additional_count = 0
        if category == "REGULAR":
            additional_count = expansion_settings['regular']
        elif category == "IMPORTANT":
            additional_count = expansion_settings['important']
        elif category == "BOSS":
            additional_count = expansion_settings['boss']

        # GYM LEADERS SPECIFIC: Log all 8 gym leaders - CORREÇÃO: IDs em DECIMAL
        gym_leader_names = {
            414: "BROCK",      # Gym Leader 1 - Rock (0x19E)
            415: "MISTY",      # Gym Leader 2 - Water (0x19F)
            416: "LT_SURGE",   # Gym Leader 3 - Electric (0x1A0)
            417: "ERIKA",      # Gym Leader 4 - Grass (0x1A1)
            418: "KOGA",       # Gym Leader 5 - Poison (0x1A2)
            419: "SABRINA",    # Gym Leader 6 - Psychic (0x1A4)
            420: "BLAINE",     # Gym Leader 7 - Fire (0x1A3)
            350: "GIOVANNI",   # Gym Leader 8 - Ground (0x15E)
        }

        if additional_count == 0:
            return 0

        # NOVO: Tratamento para parties que já possuem 6 Pokémon
        if party_size >= 6:
            return HandleFullPartySubstitution(rom, trainer_offset, trainer_data, trainer_id, trainer_class,
                                             category, additional_count, pokemon_stats_offset)

        # Limitar ao máximo de 6 Pokémon
        max_additional = min(additional_count, 6 - party_size)
        if max_additional <= 0:
            return 0

        # Ler dados da equipe atual
        if not IsValidPointer(party_ptr):
            return 0

        party_offset = party_ptr - 0x08000000

        # Verificar se offset é válido
        rom.seek(0, 2)  # Ir para o final
        rom_size = rom.tell()
        if party_offset < 0 or party_offset >= rom_size:
            return 0

        rom.seek(party_offset)

        # Determinar tamanho da entrada baseado no party flags
        party_flags = trainer_data[0]
        entry_size = 8  # Base size
        if party_flags & 0x01:  # Custom moves
            entry_size += 8
        if party_flags & 0x02:  # Held items
            entry_size += 2

        # Ler equipe atual
        current_party_data = bytearray(rom.read(party_size * entry_size))
        if len(current_party_data) < party_size * entry_size:
            return 0

        # Encontrar Pokémon de menor nível para usar como base
        lowest_level = 100
        base_pokemon_data = None

        for i in range(party_size):
            pokemon_offset = i * entry_size
            if pokemon_offset + entry_size <= len(current_party_data):
                level = current_party_data[pokemon_offset + 1]
                if level < lowest_level:
                    lowest_level = level
                    base_pokemon_data = current_party_data[pokemon_offset:pokemon_offset + entry_size]

        if base_pokemon_data is None:
            return 0

        # Salvar party size original para logs
        original_party_size = party_size

        # PASSO 1: ANALISAR PARTY ORIGINAL - Identificar tipos predominantes
        # USAR ROM ORIGINAL PARA ANÁLISE DE TIPOS
        party_type_analysis = AnalyzePartyTypesWithOriginalData(original_rom, current_party_data, party_size, entry_size, pokemon_stats_offset)

        # PASSO 2: ESCOLHER POKÉMON ADEQUADOS - Baseado nos tipos predominantes
        trainer_category = GetTrainerCategory(trainer_class, trainer_id)

        # GYM LEADERS SPECIFIC: Log type analysis for all gym leaders - CORREÇÃO: IDs em DECIMAL
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        if trainer_id in gym_leader_names:
            leader_name = gym_leader_names[trainer_id]
            primary = party_type_analysis.get('primary_type')
            secondary = party_type_analysis.get('secondary_type')
            type_counts = party_type_analysis.get('type_counts')
            allow_legendaries = ShouldAllowLegendariesInAdditionalSlot(trainer_category)

            primary_name = ConvertTypeNumberToName(primary) if primary else "None"
            secondary_name = ConvertTypeNumberToName(secondary) if secondary else "None"

        base_level = base_pokemon_data[1]  # Level do template

        additional_pokemon_list = SelectAdditionalPokemonWithAppropriateTypes(
            rom, party_type_analysis, trainer_class, trainer_id, trainer_category,
            max_additional, base_level, pokemon_stats_offset)

        if not additional_pokemon_list:
            return 0

        # PASSO 3: ADICIONAR À PARTY - Pokémon já randomizados com tipos corretos
        new_party_data = bytearray(current_party_data)

        for i, selected_species in enumerate(additional_pokemon_list):
            # CORREÇÃO: Inserir no final da party (não antes do último)
            insert_index = party_size

            # Criar novo Pokémon baseado no template, mas com species correto
            new_pokemon = bytearray(base_pokemon_data)

            # Atualizar species no novo Pokémon
            if 1 <= selected_species <= 1440:
                struct.pack_into('<H', new_pokemon, 4, selected_species)

                # GYM LEADERS SPECIFIC: Log all Pokemon added to gym leaders - CORREÇÃO: IDs em DECIMAL
                gym_leader_names = {
                    414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
                    418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
                }

                if trainer_id in gym_leader_names:
                    leader_name = gym_leader_names[trainer_id]
                    # CORREÇÃO: Usar dados do projeto para exibir tipos
                    project_pokemon_data = LoadProjectPokemonDatabase()
                    pokemon_data = project_pokemon_data.get(selected_species)
                    if pokemon_data:
                        type1_name = ConvertTypeNumberToName(pokemon_data['type1'])
                        type2_name = ConvertTypeNumberToName(pokemon_data['type2'])
                    else:
                        type1_name, type2_name = GetPokemonTypeNamesFromOffset(rom, selected_species, pokemon_stats_offset)

                    is_legendary = IsTrainerPokemonLegendary(selected_species)
                    legendary_text = " (LEGENDARY)" if is_legendary else ""

                # Log para lendários adicionados (outros bosses não-gym leaders)
                elif trainer_category == "BOSS" and IsTrainerPokemonLegendary(selected_species) and trainer_id not in gym_leader_names:
                    type1_name, type2_name = GetPokemonTypeNamesFromOffset(rom, selected_species, pokemon_stats_offset)

            # Inserir na posição correta
            insert_offset = insert_index * entry_size
            new_party_data = (new_party_data[:insert_offset] +
                            new_pokemon +
                            new_party_data[insert_offset:])

            party_size += 1

        # Escrever dados modificados de volta
        rom.seek(party_offset)
        rom.write(new_party_data)

        # Atualizar party size no trainer
        trainer_data[32] = party_size  # Offset correto para party size
        rom.seek(trainer_offset)
        rom.write(trainer_data)

        # Log para todos os gym leaders - CORREÇÃO: IDs em DECIMAL
        gym_leader_names = {
            414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
            418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
        }

        return max_additional

    except Exception as e:
        print(f"    ❌ Trainer {trainer_id}: Exception during expansion: {e}")
        return 0

def HandleFullPartySubstitution(rom, trainer_offset, trainer_data, trainer_id, trainer_class,
                               category, additional_count, pokemon_stats_offset):
    """Trata substituição inteligente para parties que já possuem 6 Pokémon"""
    import struct
    from collections import Counter

    try:
        # Ler dados da party
        party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
        party_size = trainer_data[32]

        if not IsValidPointer(party_ptr) or party_size != 6:
            return 0

        party_offset = party_ptr - 0x08000000
        rom.seek(party_offset)

        # Determinar tamanho da entrada
        party_flags = trainer_data[0]
        entry_size = 8
        if party_flags & 0x01:  # Custom moves
            entry_size += 8
        if party_flags & 0x02:  # Held items
            entry_size += 2

        # Ler todos os Pokémon da party
        party_data = bytearray(rom.read(party_size * entry_size))
        if len(party_data) < party_size * entry_size:
            return 0

        # Analisar party para encontrar candidato para substituição
        substitution_target = FindSubstitutionTarget(rom, party_data, party_size, entry_size, pokemon_stats_offset)

        if substitution_target is None:
            print(f"    ❌ Trainer {trainer_id}: No suitable substitution target found")
            return 0

        target_index, reason = substitution_target

        # Analisar tipos da party para escolher substituto adequado
        party_type_analysis = AnalyzePartyTypes(rom, party_data, party_size, entry_size, pokemon_stats_offset)
        trainer_category = GetTrainerCategory(trainer_class, trainer_id)

        # Obter Pokémon base para template
        base_pokemon_data = party_data[target_index * entry_size:(target_index + 1) * entry_size]
        base_level = base_pokemon_data[1]

        # Selecionar substituto adequado
        substitute_pokemon_list = SelectAdditionalPokemonWithAppropriateTypes(
            rom, party_type_analysis, trainer_class, trainer_id, trainer_category,
            1, base_level, pokemon_stats_offset)  # Apenas 1 substituto

        if not substitute_pokemon_list:
            print(f"    ❌ Trainer {trainer_id}: No suitable substitute Pokemon found")
            return 0

        substitute_species = substitute_pokemon_list[0]

        # Aplicar substituição
        new_pokemon = bytearray(base_pokemon_data)
        if 1 <= substitute_species <= 1440:
            struct.pack_into('<H', new_pokemon, 4, substitute_species)

            # Log para lendários
            if trainer_category == "BOSS" and IsTrainerPokemonLegendary(substitute_species):
                type1, type2 = GetPokemonTypesFromOffset(rom, substitute_species, pokemon_stats_offset)
                print(f"    🎯 Boss trainer {trainer_id}: Substituted with legendary #{substitute_species} (types {type1}/{type2})")

        # Escrever substituto na party
        substitute_offset = target_index * entry_size
        party_data[substitute_offset:substitute_offset + entry_size] = new_pokemon

        # Escrever party modificada de volta
        rom.seek(party_offset)
        rom.write(party_data)

        # Log apenas para Giovanni
        if trainer_id == 350:
            print(f"    ✅ GIOVANNI: Successfully substituted Pokemon in slot {target_index}")
        return 1  # 1 substituição realizada

    except Exception as e:
        print(f"    ❌ Trainer {trainer_id}: Exception during substitution: {e}")
        return 0

def GetTrainerCategory(trainer_class, trainer_id):
    """Determina a categoria de um treinador baseado na classe e ID"""

    # Boss trainers (Gym Leaders, Elite Four, Champion) - CORREÇÃO: Classes reais
    boss_classes = {
        84,   # Gym Leaders (0x54) - CLASSE REAL DOS GYM LEADERS
        88,   # Elite Four (0x58) - CLASSE REAL DA ELITE FOUR
        89,   # Champion (0x59) - CLASSE REAL DO CHAMPION
        86,   # Team Rocket Boss (0x56) - GIOVANNI ROCKET
        0x34, # Team Rocket Boss (alternativo)
        0x35, # Team Admin
    }

    # Boss trainer IDs conhecidos (Fire Red) - CORREÇÃO: IDs em DECIMAL
    boss_trainer_ids = {
        # Gym Leaders (IDs em decimal)
        414,  # Brock (0x19E)
        415,  # Misty (0x19F)
        416,  # Lt. Surge (0x1A0)
        417,  # Erika (0x1A1)
        418,  # Koga (0x1A2)
        420,  # Blaine (0x1A3)
        419,  # Sabrina (0x1A4)
        350,  # Giovanni (0x15E)

        # Giovanni adicional
        348, 349,  # (0x15C, 0x15D)

        # Elite Four Round 1
        410,  # Lorelei (0x19A)
        411,  # Bruno (0x19B)
        412,  # Agatha (0x19C)
        413,  # Lance (0x19D)

        # Elite Four Round 2
        735,  # Lorelei (0x2DF)
        736,  # Bruno (0x2E0)
        737,  # Agatha (0x2E1)
        738,  # Lance (0x2E2)

        # Champion
        265,  # Champion Blue Round 1 (0x109)
        266,  # Champion Blue Round 2 (0x10A)
    }

    # Important trainers (Rivals, story characters)
    important_classes = {
        0x04,  # Rival
        0x05,  # Professor
        0x06,  # Team Rocket
        0x33,  # Important NPC
    }

    # Important trainer IDs conhecidos - CORREÇÃO: IDs em DECIMAL
    important_trainer_ids = {
        # Rival battles (IDs em decimal)
        326, 327, 328, 329, 330, 331, 332, 333, 334,  # (0x146-0x14E)
        335, 336, 337, 338, 339, 340, 341, 342, 343,  # (0x14F-0x157)

        # Team Rocket admins (IDs em decimal)
        480, 481, 482, 483, 484,  # (0x1E0-0x1E4)
    }

    # Verificar por classe primeiro
    if trainer_class in boss_classes or trainer_id in boss_trainer_ids:
        return "BOSS"
    elif trainer_class in important_classes or trainer_id in important_trainer_ids:
        return "IMPORTANT"
    else:
        return "REGULAR"

def ReadTrainerExpansionSettings():
    """Lê as configurações de expansão de party do arquivo de configuração"""
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        import re

        # Ler configurações de expansão
        regular_match = re.search(r'ADDITIONAL_REGULAR_TRAINER_POKEMON\s+(\d+)', content)
        important_match = re.search(r'ADDITIONAL_IMPORTANT_TRAINER_POKEMON\s+(\d+)', content)
        boss_match = re.search(r'ADDITIONAL_BOSS_TRAINER_POKEMON\s+(\d+)', content)

        # Ler configuração de randomização
        randomize_only_additional = "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content

        # Ler configurações de lendários
        allow_legendaries_important = "ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_IMPORTANT TRUE" in content
        allow_legendaries_boss = "ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_BOSS TRUE" in content

        return {
            'additional_regular': int(regular_match.group(1)) if regular_match else 1,
            'additional_important': int(important_match.group(1)) if important_match else 2,
            'additional_boss': int(boss_match.group(1)) if boss_match else 3,
            'randomize_only_additional': randomize_only_additional,
            'allow_legendaries_important': allow_legendaries_important,
            'allow_legendaries_boss': allow_legendaries_boss,
        }
    except:
        # Valores padrão se não conseguir ler
        return {
            'additional_regular': 1,
            'additional_important': 2,
            'additional_boss': 3,
            'randomize_only_additional': True,
            'allow_legendaries_important': False,
            'allow_legendaries_boss': True,
        }

def ShouldRandomizeOnlyAdditionalPokemon():
    """Verifica se deve randomizar apenas Pokémon adicionais"""
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content
    except:
        return False

def ShouldAllowLegendariesInAdditionalSlot(trainer_category):
    """Verifica se lendários são permitidos em slots adicionais para esta categoria"""
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        if trainer_category == "IMPORTANT":
            return "ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_IMPORTANT TRUE" in content
        elif trainer_category == "BOSS":
            return "ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_BOSS TRUE" in content
        else:  # REGULAR
            return False  # Nunca permitir lendários para regulares
    except:
        return False

def ApplyTrainerPokemonRandomizationForAdditionalSlot(rom, original_species, level, pokemon_stats_offset, trainer_class, allow_legendaries_in_slot):
    """Aplica randomização específica para slots adicionais com tipos adequados"""

    # SEMPRE aplicar a hierarquia de tipos, mesmo para lendários
    # A diferença é apenas se lendários são permitidos como candidatos

    # CORREÇÃO: Usar função do projeto em vez de ROM
    # Se lendários não são permitidos no slot, usar randomização normal (sem lendários)
    if not allow_legendaries_in_slot:
        project_pokemon_data = LoadProjectPokemonDatabase()
        return ApplyTrainerPokemonRandomizationWithProject(
            original_species, level, project_pokemon_data, trainer_class, False, 999, 0)

    # Se lendários são permitidos, aplicar randomização com tipos adequados
    # mas permitindo lendários como candidatos
    return ApplyTrainerPokemonRandomizationWithTypedLegendaries(
        rom, original_species, level, pokemon_stats_offset, trainer_class)

def ApplyTrainerPokemonRandomizationWithTypedLegendaries(rom, original_species, level, pokemon_stats_offset, trainer_class):
    """CORREÇÃO: Aplica randomização usando dados do PROJETO, permitindo lendários"""
    import random

    # CORREÇÃO: Usar dados do projeto
    project_pokemon_data = LoadProjectPokemonDatabase()

    # Ler configurações
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Configurações (mesmas da função principal)
        use_similar_strength = "TRAINER_USE_SIMILAR_STRENGTH TRUE" in content
        use_type_themes = "TRAINER_USE_TYPE_THEMES TRUE" in content
        allow_alt_formes = "TRAINER_ALLOW_ALT_FORMES TRUE" in content
        force_fully_evolved = "TRAINER_FORCE_FULLY_EVOLVED TRUE" in content
        same_type_priority = "TRAINER_SAME_TYPE_PRIORITY TRUE" in content

        # Configurações específicas
        theme_strength = 85
        force_fully_evolved_level = 38

    except:
        # Valores padrão se não conseguir ler configuração
        use_similar_strength = True
        use_type_themes = True
        allow_alt_formes = True
        force_fully_evolved = True
        same_type_priority = True
        theme_strength = 85
        force_fully_evolved_level = 38

    # PRIORIDADE 1: Same Type Priority - tipos exatos (INCLUINDO LENDÁRIOS)
    if same_type_priority:
        # CORREÇÃO: Usar dados do projeto
        same_type_candidates = FindSameTypePokemonFromProject(
            original_species, project_pokemon_data, None, allow_alt_formes, use_similar_strength, False
        )

        if same_type_candidates:
            selected_species = random.choice(same_type_candidates)
            if force_fully_evolved and level >= force_fully_evolved_level:
                evolved_species = FullyEvolvePokemonFromProject(selected_species, project_pokemon_data, trainer_class or 0)
                return evolved_species
            return selected_species

    # PRIORIDADE 2: Partial Type Matching (INCLUINDO LENDÁRIOS)
    partial_type_candidates = FindPartialTypePokemonIncludingLegendaries(
        rom, original_species, pokemon_stats_offset,
        allow_alt_formes, use_similar_strength, False
    )

    if partial_type_candidates:
        selected_species = random.choice(partial_type_candidates)
        if force_fully_evolved and level >= force_fully_evolved_level:
            evolved_species = FullyEvolvePokemon(rom, selected_species, trainer_class or 0)
            return evolved_species
        return selected_species

    # PRIORIDADE 3: Type Themes (INCLUINDO LENDÁRIOS)
    allowed_types = None
    if use_type_themes and trainer_class:
        # Usar os mesmos temas da função principal
        trainer_type_themes = GetTrainerTypeThemes()
        if trainer_class in trainer_type_themes:
            theme_types = trainer_type_themes[trainer_class]
            if theme_types:
                roll = random.randint(1, 100)
                if roll <= theme_strength:
                    allowed_types = theme_types

    if allowed_types:
        theme_candidates = FindTypeThemePokemonIncludingLegendaries(
            rom, original_species, pokemon_stats_offset,
            allowed_types, allow_alt_formes, use_similar_strength, False
        )

        if theme_candidates:
            selected_species = random.choice(theme_candidates)
            if force_fully_evolved and level >= force_fully_evolved_level:
                evolved_species = FullyEvolvePokemon(rom, selected_species, trainer_class or 0)
                return evolved_species
            return selected_species

    # FALLBACK FINAL: Similar Strength (INCLUINDO LENDÁRIOS)
    return FindSimilarStrengthPokemonIncludingLegendaries(
        rom, original_species, pokemon_stats_offset,
        allow_alt_formes, force_fully_evolved,
        force_fully_evolved_level, level, trainer_class, False
    )

def FindSameTypePokemonIncludingLegendaries(rom, original_species, pokemon_stats_offset, allowed_types=None,
                                          allow_alt_formes=True, use_similar_strength=True, debug=False):
    """Encontra Pokémon com tipos exatos, INCLUINDO lendários"""

    # Usar a função original, mas sem bloquear lendários
    return FindSameTypePokemon(rom, original_species, pokemon_stats_offset, allowed_types,
                              block_legendaries=False, allow_alt_formes=allow_alt_formes,
                              use_similar_strength=use_similar_strength, debug=debug)

def FindPartialTypePokemonIncludingLegendaries(rom, original_species, pokemon_stats_offset,
                                             allow_alt_formes=True, use_similar_strength=True, debug=False):
    """Encontra Pokémon com tipos parciais, INCLUINDO lendários"""

    # Usar a função original, mas sem bloquear lendários
    return FindPartialTypePokemon(rom, original_species, pokemon_stats_offset,
                                 block_legendaries=False, allow_alt_formes=allow_alt_formes,
                                 use_similar_strength=use_similar_strength, debug=debug)

def FindTypeThemePokemonIncludingLegendaries(rom, original_species, pokemon_stats_offset, allowed_types,
                                           allow_alt_formes=True, use_similar_strength=True, debug=False):
    """Encontra Pokémon por tema de tipo, INCLUINDO lendários"""

    # Usar a função original, mas sem bloquear lendários
    return FindTypeThemePokemon(rom, original_species, pokemon_stats_offset, allowed_types,
                               block_legendaries=False, allow_alt_formes=allow_alt_formes,
                               use_similar_strength=use_similar_strength, debug=debug)

def FindSimilarStrengthPokemonIncludingLegendaries(rom, original_species, pokemon_stats_offset,
                                                  allow_alt_formes=True, force_fully_evolved=False,
                                                  force_fully_evolved_level=38, level=1, trainer_class=0, debug=False):
    """Encontra Pokémon por força similar, INCLUINDO lendários"""

    # Usar a função original, mas sem bloquear lendários
    return FindSimilarStrengthPokemon(rom, original_species, pokemon_stats_offset,
                                     block_legendaries=False, allow_alt_formes=allow_alt_formes,
                                     force_fully_evolved=force_fully_evolved,
                                     force_fully_evolved_level=force_fully_evolved_level,
                                     level=level, trainer_class=trainer_class, debug=debug)

def AnalyzePartyTypes(rom, party_data, party_size, entry_size, pokemon_stats_offset):
    """Analisa os tipos da party original para identificar padrões predominantes"""
    import struct
    from collections import Counter

    type_counts = Counter()
    pokemon_types = []

    # CORREÇÃO: Usar dados do projeto para análise de tipos
    project_pokemon_data = LoadProjectPokemonDatabase()

    for i in range(party_size):
        pokemon_offset = i * entry_size
        if pokemon_offset + entry_size <= len(party_data):
            # Extrair species do Pokémon
            species = struct.unpack('<H', party_data[pokemon_offset + 4:pokemon_offset + 6])[0]

            if 1 <= species <= 1440:
                # CORREÇÃO: Obter tipos dos dados do PROJETO
                pokemon_data = project_pokemon_data.get(species)
                if pokemon_data:
                    type1 = pokemon_data['type1']
                    type2 = pokemon_data['type2']
                else:
                    # Fallback para ROM se não encontrar no projeto
                    type1, type2 = GetPokemonTypesFromOffset(rom, species, pokemon_stats_offset)

                if type1 > 0:
                    type_counts[type1] += 1
                    pokemon_types.append(type1)

                if type2 > 0 and type2 != type1:
                    type_counts[type2] += 1
                    pokemon_types.append(type2)

    # Determinar tipos predominantes
    most_common_types = type_counts.most_common(3)  # Top 3 tipos

    return {
        'type_counts': type_counts,
        'pokemon_types': pokemon_types,
        'most_common_types': most_common_types,
        'primary_type': most_common_types[0][0] if most_common_types else None,
        'secondary_type': most_common_types[1][0] if len(most_common_types) > 1 else None
    }

def AnalyzePartyTypesWithOriginalData(rom, party_data, party_size, entry_size, pokemon_stats_offset):
    """Analisa os tipos da party usando dados do projeto (função que usa dados originais)"""
    # Esta função é idêntica à AnalyzePartyTypes pois já usa dados do projeto
    return AnalyzePartyTypes(rom, party_data, party_size, entry_size, pokemon_stats_offset)

def SelectAdditionalPokemonWithAppropriateTypes(rom, party_analysis, trainer_class, trainer_id,
                                               trainer_category, max_additional, base_level, pokemon_stats_offset):
    """
    CORREÇÃO CRÍTICA: Seleciona Pokémon adicionais baseado na configuração RANDOMIZE_ONLY_ADDITIONAL_POKEMON

    Se RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE:
        - Retorna Pokémon com tipos adequados MAS SEM randomizar
        - A randomização será feita na fase 2 (RandomizeTrainerPartyWithOriginalData)

    Se RANDOMIZE_ONLY_ADDITIONAL_POKEMON = FALSE:
        - Retorna Pokémon randomizados (comportamento atual)
        - A randomização acontece aqui mesmo
    """
    import random

    # VERIFICAR CONFIGURAÇÃO CRÍTICA
    randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()

    if randomize_only_additional:
        # MODO: Apenas adicionais serão randomizados (na fase 2)
        return SelectAdditionalPokemonWithoutRandomization(rom, party_analysis, trainer_class, trainer_id,
                                                          trainer_category, max_additional, base_level, pokemon_stats_offset)
    else:
        # MODO: Todos serão randomizados (comportamento atual)
        return SelectAdditionalPokemonWithRandomization(rom, party_analysis, trainer_class, trainer_id,
                                                       trainer_category, max_additional, base_level, pokemon_stats_offset)

def SelectAdditionalPokemonWithoutRandomization(rom, party_analysis, trainer_class, trainer_id,
                                               trainer_category, max_additional, base_level, pokemon_stats_offset):
    """
    CRITICAL FIX: Returns FINAL type-appropriate Pokemon (NOT placeholders)
    When RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE, this function provides the FINAL Pokemon
    that will NOT be randomized later - they are the actual selections
    """
    import random

    # Load project Pokemon database for proper selection
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        return []

    selected_pokemon = []
    primary_type = party_analysis.get('primary_type')
    secondary_type = party_analysis.get('secondary_type')
    party_types = party_analysis.get('party_types', set())

    # DEBUG: Log for gym leaders
    gym_leader_names = {
        414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
        418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
    }

    if trainer_id in gym_leader_names:
        leader_name = gym_leader_names[trainer_id]
        primary_name = ConvertTypeNumberToName(primary_type) if primary_type else "None"
        secondary_name = ConvertTypeNumberToName(secondary_type) if secondary_type else "None"
        print(f"🔍 {leader_name}: Selecting {max_additional} FINAL Pokemon (Primary: {primary_name}, Secondary: {secondary_name})")

    existing_species = set()

    # Select FINAL Pokemon with proper type matching (these will NOT be randomized later)
    for i in range(max_additional):
        selected_species = None

        # PRIORITY 1: Primary Type Priority (tipo predominante tem prioridade absoluta)
        if primary_type is not None:
            # STEP 1A: Buscar Pokemon com tipo primário PRIMEIRO
            primary_type_filtered = []
            for species_id in range(1, 1441):
                if species_id in existing_species:
                    continue
                if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
                    continue
                # Block legendaries and mythicals for additional slots
                if IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id):
                    continue

                pokemon_data = project_pokemon_data.get(species_id)
                if pokemon_data:
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')

                    # PRIORIDADE ABSOLUTA: Tipo primário (predominante)
                    if type1 == primary_type or type2 == primary_type:
                        primary_type_filtered.append(species_id)

            if primary_type_filtered:
                selected_species = random.choice(primary_type_filtered)
            # STEP 1B: Se não encontrou com tipo primário, tentar tipo secundário
            elif secondary_type is not None:
                secondary_type_filtered = []
                for species_id in range(1, 1441):
                    if species_id in existing_species:
                        continue
                    if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
                        continue
                    if IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id):
                        continue

                    pokemon_data = project_pokemon_data.get(species_id)
                    if pokemon_data:
                        type1 = pokemon_data.get('type1')
                        type2 = pokemon_data.get('type2')

                        # FALLBACK: Tipo secundário apenas se não encontrou primário
                        if type1 == secondary_type or type2 == secondary_type:
                            secondary_type_filtered.append(species_id)

                if secondary_type_filtered:
                    selected_species = random.choice(secondary_type_filtered)

        # FALLBACK: Any valid Pokemon if no type match found
        if not selected_species:
            fallback_candidates = []
            for species_id in range(1, 1441):
                if species_id not in existing_species:
                    if species_id in project_pokemon_data:
                        if not (ShouldBlockMegaGigantamax() and IsSpecialForm(species_id)):
                            if not (IsTrainerPokemonLegendary(species_id) or IsTrainerPokemonMythical(species_id)):
                                fallback_candidates.append(species_id)

            if fallback_candidates:
                selected_species = random.choice(fallback_candidates)
            else:
                selected_species = 25  # Ultimate fallback: Pikachu

        selected_pokemon.append(selected_species)
        existing_species.add(selected_species)

        if trainer_id in gym_leader_names:
            pokemon_data = project_pokemon_data.get(selected_species, {})
            type1 = pokemon_data.get('type1', 0)
            type2 = pokemon_data.get('type2', 0)
            type1_name = ConvertTypeNumberToName(type1) if type1 else "None"
            type2_name = ConvertTypeNumberToName(type2) if type2 else "None"
            print(f"    FINAL Pokemon {i+1}: #{selected_species} ({type1_name}/{type2_name})")

    return selected_pokemon

def FindRepresentativePokemonByType(target_type):
    """
    Encontra um Pokémon representativo de um tipo específico (não randomizado)
    Usado para placeholders que serão randomizados posteriormente
    """
    # Mapeamento de tipos para Pokémon representativos (Gen 1-3 básicos)
    type_representatives = {
        0: 1,    # Normal -> Bulbasaur (fallback)
        1: 56,   # Fighting -> Mankey
        2: 16,   # Flying -> Pidgey
        3: 23,   # Poison -> Ekans
        4: 27,   # Ground -> Sandshrew
        5: 74,   # Rock -> Geodude
        6: 10,   # Bug -> Caterpie
        7: 92,   # Ghost -> Gastly
        8: 81,   # Steel -> Magnemite (aproximação)
        9: 1,    # Mystery -> Bulbasaur (fallback)
        10: 4,   # Fire -> Charmander
        11: 7,   # Water -> Squirtle
        12: 1,   # Grass -> Bulbasaur
        13: 25,  # Electric -> Pikachu
        14: 63,  # Psychic -> Abra
        15: 86,  # Ice -> Seel
        16: 147, # Dragon -> Dratini
        17: 198, # Dark -> Murkrow
        23: 35,  # Fairy -> Clefairy
    }

    return type_representatives.get(target_type, 1)  # Default: Bulbasaur

def SelectAdditionalPokemonWithRandomization(rom, party_analysis, trainer_class, trainer_id,
                                           trainer_category, max_additional, base_level, pokemon_stats_offset):
    """
    FUNÇÃO ORIGINAL: Seleciona Pokémon adicionais com tipos adequados E randomiza imediatamente
    """
    import random

    selected_pokemon = []
    primary_type = party_analysis.get('primary_type')
    secondary_type = party_analysis.get('secondary_type')

    # Determinar se lendários são permitidos
    allow_legendaries = ShouldAllowLegendariesInAdditionalSlot(trainer_category)

    # CONTADOR DE LENDÁRIOS: MÁXIMO 1 POR TRAINER
    legendary_count = 0
    max_legendaries_per_trainer = 1

    # Gym leaders para debug - CORREÇÃO: IDs em DECIMAL
    gym_leader_names = {
        414: "BROCK", 415: "MISTY", 416: "LT_SURGE", 417: "ERIKA",
        418: "KOGA", 419: "SABRINA", 420: "BLAINE", 350: "GIOVANNI"
    }

    for i in range(max_additional):
        # DEBUG: Log para todos os gym leaders
        if trainer_id in gym_leader_names:
            leader_name = gym_leader_names[trainer_id]

        # PRIORIDADE 1: Pokémon com tipos COMPATÍVEIS (hierarquia inteligente)
        if primary_type:
            # Obter todos os tipos da party para compatibilidade
            party_types = set()
            type_counts = party_analysis.get('type_counts', {})
            for type_id in type_counts.keys():
                party_types.add(type_id)

            # CONTROLE DE LENDÁRIOS: Se já temos 1 lendário, bloquear novos lendários
            allow_legendaries_for_this_slot = allow_legendaries and (legendary_count < max_legendaries_per_trainer)

            candidates = FindPokemonByTypeWithCompatibility(rom, primary_type, party_types,
                                                          pokemon_stats_offset, allow_legendaries_for_this_slot, base_level)
            if trainer_id in gym_leader_names:
                leader_name = gym_leader_names[trainer_id]

                # Debug detalhado das prioridades
                if candidates:
                    project_pokemon_data = LoadProjectPokemonDatabase()
                    sample_pokemon = candidates[0]
                    sample_data = project_pokemon_data.get(sample_pokemon)
                    is_legendary = IsTrainerPokemonLegendary(sample_pokemon)
                    legendary_text = " (LEGENDARY)" if is_legendary else " (NORMAL)"
                    if sample_data:
                        type1_name = ConvertTypeNumberToName(sample_data['type1'])
                        type2_name = ConvertTypeNumberToName(sample_data['type2'])
                        
            if candidates:
                # NOVA LÓGICA: Se lendários permitidos, escolher o mais fraco
                if allow_legendaries_for_this_slot:
                    selected = SelectWeakestLegendaryFromCandidates(candidates, rom, pokemon_stats_offset)
                else:
                    selected = random.choice(candidates)

                selected_pokemon.append(selected)

                # ATUALIZAR CONTADOR DE LENDÁRIOS
                if IsTrainerPokemonLegendary(selected):
                    legendary_count += 1

                if trainer_id in gym_leader_names:
                    leader_name = gym_leader_names[trainer_id]
                    is_legendary = IsTrainerPokemonLegendary(selected)
                    legendary_text = " (LEGENDARY)" if is_legendary else " (NORMAL)"
                    if is_legendary:
                        # Mostrar BST do lendário selecionado
                        project_pokemon_data = LoadProjectPokemonDatabase()
                        pokemon_data = project_pokemon_data.get(selected)
                        bst = pokemon_data['bst'] if pokemon_data else "Unknown"
                        
                    else:
                        selected = random.choice(candidates)
                continue

        # PRIORIDADE 2: Pokémon com tipo secundário da party
        if secondary_type:
            # CONTROLE DE LENDÁRIOS: Se já temos 1 lendário, bloquear novos lendários
            allow_legendaries_for_this_slot = allow_legendaries and (legendary_count < max_legendaries_per_trainer)

            candidates = FindPokemonByType(rom, secondary_type, pokemon_stats_offset,
                                         allow_legendaries_for_this_slot, base_level)
            if candidates:
                # NOVA LÓGICA: Se lendários permitidos, escolher o mais fraco
                if allow_legendaries_for_this_slot:
                    selected = SelectWeakestLegendaryFromCandidates(candidates, rom, pokemon_stats_offset)
                else:
                    selected = random.choice(candidates)

                selected_pokemon.append(selected)

                # ATUALIZAR CONTADOR DE LENDÁRIOS
                if IsTrainerPokemonLegendary(selected):
                    legendary_count += 1

                continue

        # PRIORIDADE 3: Tema do treinador (se aplicável)
        trainer_type_themes = GetTrainerTypeThemes()
        if trainer_class in trainer_type_themes:
            theme_types = trainer_type_themes[trainer_class]
            if theme_types:
                theme_type = random.choice(theme_types)

                # CONTROLE DE LENDÁRIOS: Se já temos 1 lendário, bloquear novos lendários
                allow_legendaries_for_this_slot = allow_legendaries and (legendary_count < max_legendaries_per_trainer)

                candidates = FindPokemonByType(rom, theme_type, pokemon_stats_offset,
                                             allow_legendaries_for_this_slot, base_level)
                if candidates:
                    # NOVA LÓGICA: Se lendários permitidos, escolher o mais fraco
                    if allow_legendaries_for_this_slot:
                        selected = SelectWeakestLegendaryFromCandidates(candidates, rom, pokemon_stats_offset)
                    else:
                        selected = random.choice(candidates)

                    selected_pokemon.append(selected)

                    # ATUALIZAR CONTADOR DE LENDÁRIOS
                    if IsTrainerPokemonLegendary(selected):
                        legendary_count += 1

                    continue

        # FALLBACK: Pokémon com BST similar
        # CONTROLE DE LENDÁRIOS: Se já temos 1 lendário, bloquear novos lendários
        allow_legendaries_for_this_slot = allow_legendaries and (legendary_count < max_legendaries_per_trainer)

        candidates = FindPokemonBySimilarStrength(rom, 300, pokemon_stats_offset,
                                                allow_legendaries_for_this_slot, base_level)
        if candidates:
            # NOVA LÓGICA: Se lendários permitidos, escolher o mais fraco
            if allow_legendaries_for_this_slot:
                selected = SelectWeakestLegendaryFromCandidates(candidates, rom, pokemon_stats_offset)
            else:
                selected = random.choice(candidates)

            selected_pokemon.append(selected)

            # ATUALIZAR CONTADOR DE LENDÁRIOS
            if IsTrainerPokemonLegendary(selected):
                legendary_count += 1

        else:
            # Último recurso: Pokémon aleatório válido (NUNCA lendário)
            fallback_pokemon = random.randint(1, 386)  # Gen 1-3 como fallback
            selected_pokemon.append(fallback_pokemon)
            if trainer_id == 350:
                print(f"    ❌ GIOVANNI DEBUG: FALLBACK! Selected random #{fallback_pokemon}")

    return selected_pokemon

def SelectWeakestLegendaryFromCandidates(candidates, rom, pokemon_stats_offset):
    """Seleciona o lendário com menor BST da lista de candidatos"""
    project_pokemon_data = LoadProjectPokemonDatabase()

    legendary_candidates = []
    normal_candidates = []

    # Separar lendários dos normais
    for species in candidates:
        if IsTrainerPokemonLegendary(species):
            pokemon_data = project_pokemon_data.get(species)
            if pokemon_data:
                bst = pokemon_data['bst']
                legendary_candidates.append((species, bst))
        else:
            normal_candidates.append(species)

    # Se há lendários, escolher o mais fraco (menor BST)
    if legendary_candidates:
        # Ordenar por BST (menor primeiro)
        legendary_candidates.sort(key=lambda x: x[1])
        weakest_legendary = legendary_candidates[0][0]
        return weakest_legendary

    # Se não há lendários, escolher aleatório dos normais
    if normal_candidates:
        import random
        return random.choice(normal_candidates)

    # Fallback: primeiro da lista
    return candidates[0] if candidates else 1

def FindPokemonByTypeWithCompatibility(rom, target_type, party_types, pokemon_stats_offset, allow_legendaries, base_level):
    """Encontra Pokémon com hierarquia de compatibilidade de tipos E prioridade de lendários"""
    # CORREÇÃO: Usar dados do projeto em vez de ROM
    project_pokemon_data = LoadProjectPokemonDatabase()

    # Listas de candidatos por prioridade (LENDÁRIOS PRIMEIRO se permitidos)
    legendary_priority_1 = []  # Lendários: Tipo duplo compatível
    legendary_priority_2 = []  # Lendários: Tipo puro
    legendary_priority_3 = []  # Lendários: Tipo + qualquer

    normal_priority_1 = []     # Normais: Tipo duplo compatível
    normal_priority_2 = []     # Normais: Tipo puro
    normal_priority_3 = []     # Normais: Tipo + qualquer

    for species in range(1, 1441):
        is_legendary = IsTrainerPokemonLegendary(species)

        # Verificar se é lendário e não permitido
        if not allow_legendaries and is_legendary:
            continue

        # CORREÇÃO: Obter tipos dos dados do PROJETO
        pokemon_data = project_pokemon_data.get(species)
        if not pokemon_data:
            continue

        type1 = pokemon_data['type1']
        type2 = pokemon_data['type2']
        bst = pokemon_data['bst']

        # Verificar se tem o tipo desejado
        if type1 == target_type or type2 == target_type:
            # Aplicar filtro de BST (mais flexível para lendários)
            bst_min = 200 if not is_legendary else 400  # Lendários têm BST maior
            bst_max = 600 if not is_legendary else 900  # Aumentar limite para lendários

            if not (bst_min <= bst <= bst_max):
                continue

            # Determinar prioridade de tipo
            if type1 in party_types and type2 in party_types:
                # PRIORIDADE 1: Ambos os tipos estão na party
                if is_legendary:
                    legendary_priority_1.append(species)
                else:
                    normal_priority_1.append(species)
            elif type1 == target_type and type2 == target_type:
                # PRIORIDADE 2: Tipo puro do target
                if is_legendary:
                    legendary_priority_2.append(species)
                else:
                    normal_priority_2.append(species)
            else:
                # PRIORIDADE 3: Apenas um tipo compatível
                if is_legendary:
                    legendary_priority_3.append(species)
                else:
                    normal_priority_3.append(species)

    # PRIORIZAR LENDÁRIOS se permitidos, depois normais
    if allow_legendaries:
        if legendary_priority_1:
            return legendary_priority_1
        elif legendary_priority_2:
            return legendary_priority_2
        elif legendary_priority_3:
            return legendary_priority_3

    # Fallback para normais
    if normal_priority_1:
        return normal_priority_1
    elif normal_priority_2:
        return normal_priority_2
    else:
        return normal_priority_3

def FindPokemonByType(rom, target_type, pokemon_stats_offset, allow_legendaries, base_level):
    """Encontra Pokémon de um tipo específico usando dados do PROJETO (não ROM)"""
    candidates = []

    # CORREÇÃO: Usar dados do projeto em vez de ROM
    project_pokemon_data = LoadProjectPokemonDatabase()

    for species in range(1, 1441):
        # Verificar se é lendário
        if not allow_legendaries and IsTrainerPokemonLegendary(species):
            continue

        # CORREÇÃO: Obter tipos dos dados do PROJETO
        pokemon_data = project_pokemon_data.get(species)
        if not pokemon_data:
            continue

        type1 = pokemon_data['type1']
        type2 = pokemon_data['type2']

        # Verificar se tem o tipo desejado
        if type1 == target_type or type2 == target_type:
            # Aplicar filtro de BST similar (opcional)
            bst = pokemon_data['bst']
            if 200 <= bst <= 600:  # Range razoável
                candidates.append(species)

    return candidates

def FindPokemonBySimilarStrength(rom, target_bst, pokemon_stats_offset, allow_legendaries, base_level):
    """Encontra Pokémon com BST similar usando dados do PROJETO (não ROM)"""
    candidates = []
    tolerance = 100  # ±100 BST

    # CORREÇÃO: Usar dados do projeto em vez de ROM
    project_pokemon_data = LoadProjectPokemonDatabase()

    for species in range(1, 1441):
        # Verificar se é lendário
        if not allow_legendaries and IsTrainerPokemonLegendary(species):
            continue

        # CORREÇÃO: Obter BST dos dados do PROJETO
        pokemon_data = project_pokemon_data.get(species)
        if not pokemon_data:
            continue

        bst = pokemon_data['bst']
        if abs(bst - target_bst) <= tolerance:
            candidates.append(species)

    return candidates

def FindSubstitutionTarget(rom, party_data, party_size, entry_size, pokemon_stats_offset):
    """Encontra o melhor candidato para substituição seguindo a hierarquia:
    1º - Pokémon repetido (mesmo species)
    2º - Tipo com menor ocorrência
    3º - Pokémon com menor BST"""
    import struct
    from collections import Counter

    # Extrair informações de todos os Pokémon
    pokemon_info = []
    species_count = Counter()
    type_count = Counter()

    for i in range(party_size):
        pokemon_offset = i * entry_size
        if pokemon_offset + entry_size <= len(party_data):
            # Extrair species
            species = struct.unpack('<H', party_data[pokemon_offset + 4:pokemon_offset + 6])[0]
            level = party_data[pokemon_offset + 1]

            if 1 <= species <= 1440:
                # CORREÇÃO: Obter tipos e BST do PROJETO, não da ROM
                project_pokemon_data = LoadProjectPokemonDatabase()
                pokemon_data = project_pokemon_data.get(species)

                if pokemon_data:
                    type1 = pokemon_data['type1']
                    type2 = pokemon_data['type2']
                    bst = pokemon_data['bst']
                else:
                    # Fallback para ROM se não encontrar no projeto
                    type1, type2 = GetPokemonTypesFromOffset(rom, species, pokemon_stats_offset)
                    bst = GetPokemonBSTFromOffset(rom, species, pokemon_stats_offset)

                pokemon_info.append({
                    'index': i,
                    'species': species,
                    'level': level,
                    'type1': type1,
                    'type2': type2,
                    'bst': bst
                })

                # Contar species
                species_count[species] += 1

                # Contar tipos
                if type1 > 0:
                    type_count[type1] += 1
                if type2 > 0 and type2 != type1:
                    type_count[type2] += 1

    if not pokemon_info:
        return None

    # PRIORIDADE 1: Pokémon repetido (mesmo species)
    for species, count in species_count.items():
        if count > 1:
            # Encontrar o primeiro Pokémon repetido
            for pokemon in pokemon_info:
                if pokemon['species'] == species:
                    return (pokemon['index'], f"repeated species #{species}")

    # PRIORIDADE 2: Tipo com menor ocorrência
    if type_count:
        # Encontrar tipos menos comuns
        least_common_types = type_count.most_common()
        if least_common_types:
            # Pegar o tipo com menor ocorrência
            least_common_type, min_count = least_common_types[-1]

            # Encontrar Pokémon com esse tipo
            for pokemon in pokemon_info:
                if pokemon['type1'] == least_common_type or pokemon['type2'] == least_common_type:
                    return (pokemon['index'], f"least common type {least_common_type} (count: {min_count})")

    # PRIORIDADE 3: Pokémon com menor BST
    if pokemon_info:
        weakest_pokemon = min(pokemon_info, key=lambda p: p['bst'])
        return (weakest_pokemon['index'], f"lowest BST {weakest_pokemon['bst']}")

    return None

def GetTrainerTypeThemes():
    """Retorna o mapeamento de classes de treinador para tipos temáticos"""
    # Constantes de tipos (baseado em include/types.h)
    TYPE_NORMAL = 0
    TYPE_FIGHTING = 1
    TYPE_FLYING = 2
    TYPE_POISON = 3
    TYPE_GROUND = 4
    TYPE_ROCK = 5
    TYPE_BUG = 6
    TYPE_GHOST = 7
    TYPE_STEEL = 8
    TYPE_MYSTERY = 9
    TYPE_FIRE = 10
    TYPE_WATER = 11
    TYPE_GRASS = 12
    TYPE_ELECTRIC = 13
    TYPE_PSYCHIC = 14
    TYPE_ICE = 15
    TYPE_DRAGON = 16
    TYPE_DARK = 17
    TYPE_FAIRY = 23

    # Mapeamento de classes de treinador para tipos temáticos
    trainer_type_themes = {
        # TRAINER_CLASS_BUG_CATCHER = 2
        2: [TYPE_BUG],  # Bug type
        # TRAINER_CLASS_SAILOR = 4
        4: [TYPE_WATER, TYPE_FIGHTING],  # Water, Fighting types
        # TRAINER_CLASS_SUPER_NERD = 8
        8: [TYPE_ELECTRIC, TYPE_PSYCHIC],  # Electric, Psychic types
        # TRAINER_CLASS_HIKER = 9
        9: [TYPE_ROCK, TYPE_GROUND],  # Rock, Ground types
        # TRAINER_CLASS_BIKER = 10
        10: [TYPE_POISON],   # Poison type
        # TRAINER_CLASS_BURGLAR = 11
        11: [TYPE_FIRE, TYPE_POISON],  # Fire, Poison types
        # TRAINER_CLASS_FISHERMAN = 12
        12: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_SWIMMER_M = 13
        13: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_CUE_BALL = 14
        14: [TYPE_FIGHTING],  # Fighting type
        # TRAINER_CLASS_PSYCHIC_M = 15
        15: [TYPE_PSYCHIC],  # Psychic type
        # TRAINER_CLASS_ROCKER = 16
        16: [TYPE_ELECTRIC],  # Electric type
        # TRAINER_CLASS_JUGGLER = 17
        17: [TYPE_PSYCHIC],  # Psychic type
        # TRAINER_CLASS_TAMER = 18
        18: [TYPE_NORMAL],  # Normal type
        # TRAINER_CLASS_BIRD_KEEPER = 19
        19: [TYPE_FLYING, TYPE_NORMAL],  # Flying, Normal types
        # TRAINER_CLASS_BLACKBELT = 20
        20: [TYPE_FIGHTING],  # Fighting type
        # TRAINER_CLASS_RIVAL1 = 21
        21: [],  # No theme (rival)
        # TRAINER_CLASS_SCIENTIST = 22
        22: [TYPE_ELECTRIC, TYPE_PSYCHIC],  # Electric, Psychic types
        # TRAINER_CLASS_GIOVANNI = 23
        23: [TYPE_GROUND, TYPE_ROCK],  # Ground, Rock types
        # TRAINER_CLASS_ROCKET = 24
        24: [TYPE_POISON, TYPE_DARK],  # Poison, Dark types
        # TRAINER_CLASS_COOLTRAINER_M = 25
        25: [],  # No theme (varied)
        # TRAINER_CLASS_COOLTRAINER_F = 26
        26: [],  # No theme (varied)
        # TRAINER_CLASS_BRUNO = 27
        27: [TYPE_FIGHTING, TYPE_ROCK],  # Fighting, Rock types
        # TRAINER_CLASS_BROCK = 28
        28: [TYPE_ROCK, TYPE_GROUND],  # Rock, Ground types
        # TRAINER_CLASS_MISTY = 29
        29: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_LT_SURGE = 30
        30: [TYPE_ELECTRIC],  # Electric type
        # TRAINER_CLASS_ERIKA = 31
        31: [TYPE_GRASS],  # Grass type
        # TRAINER_CLASS_KOGA = 32
        32: [TYPE_POISON],  # Poison type
        # TRAINER_CLASS_BLAINE = 33
        33: [TYPE_FIRE],  # Fire type
        # TRAINER_CLASS_SABRINA = 34
        34: [TYPE_PSYCHIC],  # Psychic type
        # TRAINER_CLASS_GENTLEMAN = 35
        35: [],  # No theme
        # TRAINER_CLASS_RIVAL2 = 36
        36: [],  # No theme (rival)
        # TRAINER_CLASS_CHAMPION = 37
        37: [],  # No theme (varied)
        # TRAINER_CLASS_LORELEI = 38
        38: [TYPE_ICE, TYPE_WATER],  # Ice, Water types
        # TRAINER_CLASS_CHANNELER = 39
        39: [TYPE_GHOST, TYPE_PSYCHIC],  # Ghost, Psychic types
        # TRAINER_CLASS_AGATHA = 40
        40: [TYPE_GHOST, TYPE_POISON],  # Ghost, Poison types
        # TRAINER_CLASS_LANCE = 41
        41: [TYPE_DRAGON, TYPE_FLYING],  # Dragon, Flying types
        # TRAINER_CLASS_SWIMMER_F = 42
        42: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_PICNICKER = 43
        43: [],  # No theme
        # TRAINER_CLASS_TWINS = 44
        44: [TYPE_PSYCHIC],  # Psychic type
        # TRAINER_CLASS_SAILOR2 = 45
        45: [TYPE_WATER, TYPE_FIGHTING],  # Water, Fighting types
        # TRAINER_CLASS_RUIN_MANIAC = 46
        46: [TYPE_GROUND, TYPE_ROCK],  # Ground, Rock types
        # TRAINER_CLASS_LADY = 47
        47: [],  # No theme
        # TRAINER_CLASS_PAINTER = 48
        48: [],  # No theme
        # TRAINER_CLASS_AROMA_LADY = 49
        49: [TYPE_GRASS],  # Grass type
        # TRAINER_CLASS_TUBER_F = 50
        50: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_TUBER_M = 51
        51: [TYPE_WATER],  # Water type
        # TRAINER_CLASS_PKMN_BREEDER_F = 52
        52: [],  # No theme (varied)
        # TRAINER_CLASS_PKMN_BREEDER_M = 53
        53: [],  # No theme (varied)
        # TRAINER_CLASS_PKMN_RANGER_F = 54
        54: [],  # No theme (varied)
        # TRAINER_CLASS_PKMN_RANGER_M = 55
        55: [],  # No theme (varied)
    }

    return trainer_type_themes

# ===== SISTEMA DE DADOS DO PROJETO =====

# CORREÇÃO: Limpar cache para aplicar correção de tipos
_project_pokemon_cache = None

def LoadProjectPokemonDatabase():
    """Carrega dados dos Pokémon do projeto (Base_Stats.c) - CORREÇÃO ARQUITETURAL"""
    global _project_pokemon_cache

    # CORREÇÃO: Cache reativado com filtro de bloqueio aplicado
    if _project_pokemon_cache is not None:
        return _project_pokemon_cache

    pokemon_data = {}

    try:
        # Ler Base_Stats.c
        with open("src/Base_Stats.c", "r", encoding="utf-8", errors="ignore") as f:
            content = f.read()

        # Ler species.h para mapeamento de IDs
        species_mapping = LoadSpeciesMapping()

        # Parse das entradas do Base_Stats.c
        pokemon_data = ParseBaseStatsFile(content, species_mapping)

        _project_pokemon_cache = pokemon_data
        print(f"✅ Loaded {len(pokemon_data)} Pokemon from project database")

    except Exception as e:
        print(f"❌ Error loading project Pokemon database: {e}")
        # Fallback: retornar cache vazio
        _project_pokemon_cache = {}

    return _project_pokemon_cache

def LoadSpeciesMapping():
    """Carrega mapeamento de nomes para IDs do species.h"""
    species_mapping = {}

    try:
        with open("include/species.h", "r", encoding="utf-8", errors="ignore") as f:
            content = f.read()

        import re
        # Procurar por #define SPECIES_NAME ID
        pattern = r'#define\s+SPECIES_(\w+)\s+(0x[0-9A-Fa-f]+|\d+)'
        matches = re.findall(pattern, content)

        for name, id_str in matches:
            if id_str.startswith('0x'):
                species_id = int(id_str, 16)
            else:
                species_id = int(id_str)
            species_mapping[name] = species_id

    except Exception as e:
        print(f"❌ Error loading species mapping: {e}")

    return species_mapping

def ParseBaseStatsFile(content, species_mapping):
    """Parse do arquivo Base_Stats.c para extrair dados dos Pokémon"""
    pokemon_data = {}

    import re

    # Pattern para encontrar entradas de Pokémon
    # [SPECIES_NAME] = { ... }
    pattern = r'\[SPECIES_(\w+)\]\s*=\s*\{([^}]+)\}'
    matches = re.findall(pattern, content, re.DOTALL)

    for species_name, stats_block in matches:
        species_id = species_mapping.get(species_name)
        if not species_id:
            continue

        # CORREÇÃO: Aplicar filtro de bloqueio durante carregamento
        if ShouldBlockMegaGigantamax() and IsSpecialForm(species_id):
            continue

        # Parse dos stats individuais
        stats_data = ParsePokemonStatsBlock(stats_block)
        if stats_data:
            # CORREÇÃO: Adicionar nome do Pokémon
            stats_data['name'] = species_name
            pokemon_data[species_id] = stats_data

    return pokemon_data

def ParsePokemonStatsBlock(stats_block):
    """Parse de um bloco de stats individual"""
    import re

    try:
        # Extrair valores usando regex
        stats = {}

        # Stats básicos
        hp_match = re.search(r'\.baseHP\s*=\s*(\d+)', stats_block)
        attack_match = re.search(r'\.baseAttack\s*=\s*(\d+)', stats_block)
        defense_match = re.search(r'\.baseDefense\s*=\s*(\d+)', stats_block)
        speed_match = re.search(r'\.baseSpeed\s*=\s*(\d+)', stats_block)
        spatk_match = re.search(r'\.baseSpAttack\s*=\s*(\d+)', stats_block)
        spdef_match = re.search(r'\.baseSpDefense\s*=\s*(\d+)', stats_block)

        # Tipos
        type1_match = re.search(r'\.type1\s*=\s*TYPE_(\w+)', stats_block)
        type2_match = re.search(r'\.type2\s*=\s*TYPE_(\w+)', stats_block)

        if not all([hp_match, attack_match, defense_match, speed_match, spatk_match, spdef_match]):
            return None

        # Calcular BST
        hp = int(hp_match.group(1))
        attack = int(attack_match.group(1))
        defense = int(defense_match.group(1))
        speed = int(speed_match.group(1))
        spatk = int(spatk_match.group(1))
        spdef = int(spdef_match.group(1))

        bst = hp + attack + defense + speed + spatk + spdef

        # Converter tipos para números
        type1 = ConvertTypeNameToNumber(type1_match.group(1) if type1_match else "NORMAL")
        type2 = ConvertTypeNameToNumber(type2_match.group(1) if type2_match else "NORMAL")

        return {
            'hp': hp,
            'attack': attack,
            'defense': defense,
            'speed': speed,
            'spatk': spatk,
            'spdef': spdef,
            'bst': bst,
            'type1': type1,
            'type2': type2,
            'name': 'Unknown'  # CORREÇÃO: Adicionar campo name (será preenchido depois)
        }

    except Exception as e:
        return None

def ConvertTypeNameToNumber(type_name):
    """Converte nome do tipo para número - CORREÇÃO: Mapeamento correto dos tipos"""
    type_mapping = {
        'NORMAL': 0x00,     # 0
        'FIGHTING': 0x01,   # 1
        'FLYING': 0x02,     # 2
        'POISON': 0x03,     # 3
        'GROUND': 0x04,     # 4
        'ROCK': 0x05,       # 5
        'BUG': 0x06,        # 6
        'GHOST': 0x07,      # 7
        'STEEL': 0x08,      # 8
        'FIRE': 0x09,       # 9 - CORREÇÃO: Era 0x0A (10)
        'WATER': 0x0A,      # 10 - CORREÇÃO: Era 0x0B (11)
        'GRASS': 0x0B,      # 11 - CORREÇÃO: Era 0x0C (12)
        'ELECTRIC': 0x0C,   # 12 - CORREÇÃO: Era 0x0D (13)
        'PSYCHIC': 0x0D,    # 13 - CORREÇÃO: Era 0x0E (14)
        'ICE': 0x0E,        # 14 - CORREÇÃO: Era 0x0F (15)
        'DRAGON': 0x0F,     # 15 - CORREÇÃO: Era 0x10 (16)
        'DARK': 0x10,       # 16 - CORREÇÃO: Era 0x11 (17)
        'ROOSTLESS': 0x13,  # 19
        'BLANK': 0x14,      # 20
        'FAIRY': 0x17,      # 23
    }

    return type_mapping.get(type_name, 0x00)  # Default: Normal

def ConvertTypeNumberToName(type_number):
    """Converte número do tipo para nome - CORREÇÃO: Mapeamento correto dos tipos"""
    type_mapping = {
        0x00: "Normal",     # 0
        0x01: "Fighting",   # 1
        0x02: "Flying",     # 2
        0x03: "Poison",     # 3
        0x04: "Ground",     # 4
        0x05: "Rock",       # 5
        0x06: "Bug",        # 6
        0x07: "Ghost",      # 7
        0x08: "Steel",      # 8
        0x09: "Fire",       # 9 - CORREÇÃO: Era Mystery
        0x0A: "Water",      # 10 - CORREÇÃO: Era Fire
        0x0B: "Grass",      # 11 - CORREÇÃO: Era Water
        0x0C: "Electric",   # 12 - CORREÇÃO: Era Grass
        0x0D: "Psychic",    # 13 - CORREÇÃO: Era Electric
        0x0E: "Ice",        # 14 - CORREÇÃO: Era Psychic
        0x0F: "Dragon",     # 15 - CORREÇÃO: Era Ice
        0x10: "Dark",       # 16 - CORREÇÃO: Era Dragon
        0x13: "Roostless",  # 19
        0x14: "Blank",      # 20
        0x17: "Fairy",      # 23
    }

    return type_mapping.get(type_number, f"Unknown({type_number})")

if __name__ == '__main__':
    main()
