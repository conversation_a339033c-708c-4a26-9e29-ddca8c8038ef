#!/usr/bin/env python3
"""
Teste da correção da estrutura de 16 bytes
Verificar se o Koga agora mostra dados corretos
"""

import sys
import struct
sys.path.append('scripts')

def test_16byte_fix():
    """Testa se a correção de 16 bytes funciona"""
    
    print("🔍 TESTE: CORREÇÃO DA ESTRUTURA DE 16 BYTES")
    print("=" * 60)
    
    try:
        from insert import LoadProjectPokemonDatabase, ConvertTypeNumberToName
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        with open('BPRE0.gba', 'rb') as rom:
            # Ko<PERSON>'s data
            trainer_table_offset = 0x23EAC8
            koga_trainer_id = 418
            trainer_entry_size = 40
            koga_offset = trainer_table_offset + (koga_trainer_id * trainer_entry_size)
            
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_pointer = struct.unpack('<I', trainer_data[36:40])[0]
            
            if party_pointer >= 0x08000000:
                party_file_offset = party_pointer - 0x08000000
            else:
                party_file_offset = party_pointer
            
            print(f"🎯 KOGA ANALYSIS (16-byte structure):")
            print(f"   Party offset: 0x{party_file_offset:08X}")
            print(f"   Party size: {party_size}")
            print()
            
            # Test with 16-byte structure
            print(f"📊 ESTRUTURA DE 16 BYTES:")
            print("-" * 40)
            
            rom.seek(party_file_offset)
            
            for slot in range(party_size):
                pokemon_offset = party_file_offset + (slot * 16)  # FIXED: 16 bytes
                
                rom.seek(pokemon_offset)
                pokemon_data = rom.read(16)  # FIXED: Read 16 bytes
                
                if len(pokemon_data) < 16:
                    continue
                
                # Parse data
                iv = pokemon_data[0]
                level = pokemon_data[2]
                species = struct.unpack('<H', pokemon_data[4:6])[0]
                held_item = struct.unpack('<H', pokemon_data[6:8])[0]
                
                # Get Pokemon info
                pokemon_name = "Unknown"
                type1_name = "Unknown"
                type2_name = "None"
                
                if species in project_pokemon_data:
                    pokemon_info = project_pokemon_data[species]
                    pokemon_name = pokemon_info.get('name', f'Unknown #{species}')
                    type1 = pokemon_info.get('type1')
                    type2 = pokemon_info.get('type2')
                    
                    type1_name = ConvertTypeNumberToName(type1) if type1 is not None else "Unknown"
                    type2_name = ConvertTypeNumberToName(type2) if type2 is not None and type2 != type1 else "None"
                
                # Validate data
                valid_level = 1 <= level <= 100
                valid_species = 1 <= species <= 1440
                valid_data = valid_level and valid_species
                
                status = "✅ VALID" if valid_data else "❌ INVALID"
                
                print(f"Slot {slot + 1}: {status}")
                print(f"   Offset: 0x{pokemon_offset:08X}")
                print(f"   Raw: {pokemon_data.hex()}")
                print(f"   IV: {iv}")
                print(f"   Level: {level} {'(INVALID)' if not valid_level else ''}")
                print(f"   Species: #{species} {pokemon_name} {'(INVALID)' if not valid_species else ''}")
                print(f"   Types: {type1_name}/{type2_name}")
                print(f"   Held Item: {held_item}")
                print()
            
            # Expected Koga party with 16-byte structure
            expected_koga_16byte = [
                {"name": "KOFFING", "species": 109, "level": 37, "types": "POISON/None"},
                {"name": "MUK", "species": 89, "level": 39, "types": "POISON/None"},
                {"name": "KOFFING", "species": 109, "level": 37, "types": "POISON/None"},
                {"name": "WEEZING", "species": 110, "level": 43, "types": "POISON/None"},
            ]
            
            print("🎯 COMPARAÇÃO COM ESPERADO:")
            print("-" * 40)
            
            all_valid = True
            
            for slot in range(min(party_size, len(expected_koga_16byte))):
                pokemon_offset = party_file_offset + (slot * 16)
                
                rom.seek(pokemon_offset)
                data = rom.read(16)
                level = data[2]
                species = struct.unpack('<H', data[4:6])[0]
                
                expected = expected_koga_16byte[slot]
                
                level_match = 1 <= level <= 100
                species_match = species == expected['species']
                
                if level_match and species_match:
                    print(f"Slot {slot + 1}: ✅ MATCH - {expected['name']} Level {level}")
                else:
                    print(f"Slot {slot + 1}: ❌ MISMATCH")
                    print(f"   Expected: {expected['name']} (#{expected['species']}) Level {expected['level']}")
                    print(f"   Found:    Species #{species}, Level {level}")
                    all_valid = False
            
            print()
            print("🎯 RESULTADO FINAL:")
            print("-" * 40)
            
            if all_valid:
                print("✅ CORREÇÃO FUNCIONOU!")
                print("✅ Estrutura de 16 bytes produz dados válidos")
                print("✅ Koga agora tem Pokemon corretos")
                print("✅ Levels estão dentro do range válido (1-100)")
                print()
                print("🔧 PRÓXIMOS PASSOS:")
                print("1. ✅ Estrutura corrigida para 16 bytes")
                print("2. 🔄 Testar randomização do trainer")
                print("3. 🔄 Verificar se Pokemon corretos aparecem no jogo")
            else:
                print("❌ AINDA HÁ PROBLEMAS")
                print("❌ Dados não correspondem ao esperado")
                print("❌ Pode haver outros problemas na ROM")
            
            return all_valid
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_16byte_fix()
