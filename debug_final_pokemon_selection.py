#!/usr/bin/env python3
"""
Debug final: Testar a função SelectPokemonFromProjectDatabase diretamente
"""

import sys
import os

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_final_pokemon_selection():
    """Testa a função SelectPokemonFromProjectDatabase diretamente"""
    
    print("🔍 DEBUG FINAL: Testando SelectPokemonFromProjectDatabase")
    print("=" * 60)
    
    try:
        from insert import (
            SelectPokemonFromProjectDatabase,
            LoadProjectPokemonDatabase
        )
        
        # Simular dados de Koga
        koga_primary_type = 3  # POISON
        koga_secondary_type = 7  # GHOST
        koga_party_types = {3, 7}  # POISON, GHOST
        existing_species = {109, 92, 89}  # Koffing, Gastly, Muk
        level = 40
        trainer_class = 84  # Gym Leader
        trainer_id = 418  # Koga
        
        print(f"🎯 SIMULAÇÃO PARA KOGA:")
        print(f"   Primary type: {koga_primary_type} (POISON)")
        print(f"   Secondary type: {koga_secondary_type} (GHOST)")
        print(f"   Party types: {koga_party_types}")
        print(f"   Existing species: {existing_species}")
        print(f"   Level: {level}")
        print(f"   Trainer class: {trainer_class}")
        print(f"   Trainer ID: {trainer_id}")
        
        # Testar seleção múltiplas vezes
        print(f"\n🧪 TESTANDO SELEÇÃO 10 VEZES:")
        
        for i in range(10):
            selected = SelectPokemonFromProjectDatabase(
                koga_primary_type, koga_secondary_type, koga_party_types,
                existing_species, level, trainer_class, trainer_id
            )
            
            print(f"   Teste {i+1}: Species #{selected}")
            
            # Verificar se é Ponyta
            if selected == 77:
                print(f"      🚨 PONYTA ENCONTRADO! Investigando...")
                
                # Verificar dados do projeto para Ponyta
                project_pokemon_data = LoadProjectPokemonDatabase()
                if 77 in project_pokemon_data:
                    ponyta_data = project_pokemon_data[77]
                    type1 = ponyta_data.get('type1')
                    type2 = ponyta_data.get('type2')
                    
                    print(f"      🔍 Ponyta no projeto: Type1={type1}, Type2={type2}")
                    
                    if type1 == 3 or type2 == 3:  # POISON
                        print(f"      ✅ Ponyta TEM tipo POISON no projeto!")
                    else:
                        print(f"      ❌ Ponyta NÃO tem tipo POISON no projeto!")
            
            # Verificar se é Species válido
            if selected:
                project_pokemon_data = LoadProjectPokemonDatabase()
                if selected in project_pokemon_data:
                    pokemon_data = project_pokemon_data[selected]
                    type1 = pokemon_data.get('type1')
                    type2 = pokemon_data.get('type2')
                    
                    # Verificar se combina com Koga
                    matches = []
                    if type1 in koga_party_types:
                        matches.append(f"Type1={type1}")
                    if type2 in koga_party_types:
                        matches.append(f"Type2={type2}")
                    
                    if matches:
                        print(f"      ✅ Combina: {', '.join(matches)}")
                    else:
                        print(f"      ❌ NÃO combina: Type1={type1}, Type2={type2}")
            
            # Adicionar à lista de existentes para próximo teste
            if selected:
                existing_species.add(selected)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ponyta_in_project():
    """Testa especificamente os dados de Ponyta no projeto"""
    
    print(f"\n🐴 TESTE ESPECÍFICO: Dados de Ponyta no Projeto")
    print("=" * 50)
    
    try:
        from insert import LoadProjectPokemonDatabase
        
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        ponyta_species = 77
        
        if ponyta_species in project_pokemon_data:
            ponyta_data = project_pokemon_data[ponyta_species]
            
            print(f"✅ Ponyta encontrado no projeto:")
            print(f"   Species: {ponyta_species}")
            print(f"   Name: {ponyta_data.get('name', 'Unknown')}")
            print(f"   Type1: {ponyta_data.get('type1')} (3=POISON, 9=FIRE)")
            print(f"   Type2: {ponyta_data.get('type2')}")
            print(f"   BST: {ponyta_data.get('bst')}")
            print(f"   Is Legendary: {ponyta_data.get('is_legendary', False)}")
            
            # Verificar se Ponyta tem tipo POISON no projeto
            type1 = ponyta_data.get('type1')
            type2 = ponyta_data.get('type2')
            
            if type1 == 3 or type2 == 3:  # POISON = 3
                print(f"   🎯 PONYTA TEM TIPO POISON NO PROJETO!")
                print(f"   🔍 Isso explica por que foi selecionado para Koga")
            elif type1 == 9:  # FIRE = 9
                print(f"   🔥 PONYTA É FIRE TYPE NO PROJETO")
                print(f"   🚨 NÃO deveria ser selecionado para Koga (POISON)")
            else:
                print(f"   ❓ PONYTA tem tipos inesperados: {type1}, {type2}")
        else:
            print(f"❌ Ponyta não encontrado no projeto")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_configuration():
    """Verifica a configuração RANDOMIZE_ONLY_ADDITIONAL_POKEMON"""
    
    print(f"\n⚙️ VERIFICAÇÃO: Configuração RANDOMIZE_ONLY_ADDITIONAL_POKEMON")
    print("=" * 60)
    
    try:
        from insert import ShouldRandomizeOnlyAdditionalPokemon
        
        result = ShouldRandomizeOnlyAdditionalPokemon()
        
        print(f"🎯 RESULTADO: ShouldRandomizeOnlyAdditionalPokemon() = {result}")
        
        if result:
            print(f"   ✅ Configuração CORRETA: Apenas Pokémon adicionais serão randomizados")
            print(f"   🔍 Isso significa que a party original de Koga NÃO deve ser modificada")
            print(f"   🔍 Apenas os slots adicionais (5 e 6) devem receber novos Pokémon")
        else:
            print(f"   ⚠️ Configuração: TODOS os Pokémon serão randomizados")
            print(f"   🔍 Isso pode explicar por que Ponyta aparece")
        
        # Verificar arquivo de configuração diretamente
        config_file = "include/wild_encounters_config.h"
        try:
            with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content:
                print(f"   📋 Arquivo de config: RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE")
            elif "RANDOMIZE_ONLY_ADDITIONAL_POKEMON FALSE" in content:
                print(f"   📋 Arquivo de config: RANDOMIZE_ONLY_ADDITIONAL_POKEMON FALSE")
            else:
                print(f"   📋 Arquivo de config: RANDOMIZE_ONLY_ADDITIONAL_POKEMON não encontrado")
        
        except Exception as e:
            print(f"   ❌ Erro ao ler arquivo de config: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar debug
    debug_final_pokemon_selection()
    test_ponyta_in_project()
    check_configuration()
