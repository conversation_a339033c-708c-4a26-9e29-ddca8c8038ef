#!/usr/bin/env python3
"""
Debug crítico: Investigar confusão entre Pokédex Nacional e Species ID do projeto
"""

import sys
import os
import struct

# Adicionar o diretório scripts ao path
sys.path.append('scripts')

def debug_id_confusion():
    """Investiga a confusão entre IDs"""
    
    print("🚨 DEBUG CRÍTICO: Confusão de IDs Pokédex vs Species")
    print("=" * 60)
    
    try:
        from insert import (
            LoadProjectPokemonDatabase,
            FindTrainerTable,
            ReadAllTrainerDataFromOriginalROM
        )
        
        # Carregar dados do projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        print(f"🔍 INVESTIGAÇÃO 1: Verificar IDs específicos")
        print("-" * 50)
        
        # Verificar IDs específicos mencionados
        test_ids = [3, 58, 77, 92, 109, 643, 696]
        
        for species_id in test_ids:
            if species_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[species_id]
                name = pokemon_data.get('name', 'Unknown')
                print(f"   Species #{species_id:3d}: {name}")
            else:
                print(f"   Species #{species_id:3d}: NÃO ENCONTRADO no projeto")
        
        print(f"\n🔍 INVESTIGAÇÃO 2: Verificar SPECIES_RESHIRAM")
        print("-" * 50)
        
        reshiram_species_id = 0x2B8  # 696 decimal
        print(f"   SPECIES_RESHIRAM = 0x{reshiram_species_id:03X} = {reshiram_species_id}")
        
        if reshiram_species_id in project_pokemon_data:
            reshiram_data = project_pokemon_data[reshiram_species_id]
            name = reshiram_data.get('name', 'Unknown')
            print(f"   ✅ Species #{reshiram_species_id}: {name}")
        else:
            print(f"   ❌ Species #{reshiram_species_id}: NÃO ENCONTRADO")
        
        # Verificar se #643 existe no projeto
        if 643 in project_pokemon_data:
            pokemon_643_data = project_pokemon_data[643]
            name_643 = pokemon_643_data.get('name', 'Unknown')
            print(f"   Species #643: {name_643}")
        
        print(f"\n🔍 INVESTIGAÇÃO 3: Party Original de Koga na ROM")
        print("-" * 50)
        
        # Ler dados originais de Koga
        original_cache = ReadAllTrainerDataFromOriginalROM()
        if original_cache and 'trainers' in original_cache:
            koga_id = 418
            if koga_id in original_cache['trainers']:
                koga_info = original_cache['trainers'][koga_id]
                original_party = koga_info['original_party']
                
                print(f"   Party original de Koga (do cache):")
                for i, pokemon in enumerate(original_party):
                    species = pokemon.get('species', 'Unknown')
                    level = pokemon.get('level', 'Unknown')
                    
                    # Verificar nome no projeto
                    if species in project_pokemon_data:
                        pokemon_data = project_pokemon_data[species]
                        name = pokemon_data.get('name', 'Unknown')
                        print(f"      Slot {i+1}: Species #{species} ({name}) Level {level}")
                    else:
                        print(f"      Slot {i+1}: Species #{species} (NÃO ENCONTRADO) Level {level}")
        
        print(f"\n🔍 INVESTIGAÇÃO 4: Leitura Direta da ROM")
        print("-" * 50)
        
        # Ler diretamente da ROM para comparar
        with open("test.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            koga_id = 418
            
            # Ler dados do trainer Koga
            trainer_offset = trainer_table_offset + (koga_id * 40)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            party_offset = party_ptr - 0x08000000
            
            print(f"   Koga - Party size: {party_size}, offset: 0x{party_offset:08X}")
            
            # Ler party atual da ROM
            rom.seek(party_offset)
            party_data = rom.read(party_size * 8)
            
            print(f"   Party atual na ROM:")
            for i in range(party_size):
                pokemon_offset = i * 8
                pokemon_data = party_data[pokemon_offset:pokemon_offset + 8]
                
                if len(pokemon_data) >= 8:
                    level = pokemon_data[2]
                    species_id = struct.unpack('<H', pokemon_data[4:6])[0]
                    
                    # Verificar nome no projeto
                    if species_id in project_pokemon_data:
                        pokemon_data_proj = project_pokemon_data[species_id]
                        name = pokemon_data_proj.get('name', 'Unknown')
                        print(f"      Slot {i+1}: Species #{species_id} ({name}) Level {level}")
                    else:
                        print(f"      Slot {i+1}: Species #{species_id} (NÃO ENCONTRADO) Level {level}")
        
        print(f"\n🔍 INVESTIGAÇÃO 5: Verificar Conversões de ID")
        print("-" * 50)
        
        # Verificar se há funções de conversão
        try:
            from insert import SpeciesToNationalPokedexNum, NationalPokedexNumToSpecies
            
            print(f"   Funções de conversão encontradas:")
            
            # Testar conversões
            test_species = [3, 77, 109, 643, 696]
            for species in test_species:
                try:
                    national_num = SpeciesToNationalPokedexNum(species)
                    print(f"      Species #{species} -> National #{national_num}")
                except:
                    print(f"      Species #{species} -> Conversão falhou")
            
            # Testar conversão reversa
            test_national = [3, 77, 109, 643]
            for national in test_national:
                try:
                    species_num = NationalPokedexNumToSpecies(national)
                    print(f"      National #{national} -> Species #{species_num}")
                except:
                    print(f"      National #{national} -> Conversão falhou")
                    
        except ImportError:
            print(f"   ❌ Funções de conversão não encontradas")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_rom_reading_offset():
    """Verifica se há offset incorreto na leitura da ROM"""
    
    print(f"\n🔍 INVESTIGAÇÃO 6: Verificar Offset na Leitura")
    print("-" * 50)
    
    try:
        from insert import LoadProjectPokemonDatabase
        
        # Verificar se há offset sendo aplicado incorretamente
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        # Simular leitura com diferentes offsets
        test_species_raw = [109, 89, 109, 110]  # Party original esperada
        
        print(f"   Party original esperada (Pokédex Nacional):")
        for i, species in enumerate(test_species_raw):
            print(f"      Slot {i+1}: #{species} (Pokédex Nacional)")
        
        print(f"\n   Testando possíveis offsets:")
        
        # Testar diferentes offsets
        for offset in [0, 1, -1, 25, -25]:
            print(f"      Offset {offset:+3d}:")
            for i, species in enumerate(test_species_raw):
                adjusted_species = species + offset
                if adjusted_species in project_pokemon_data:
                    pokemon_data = project_pokemon_data[adjusted_species]
                    name = pokemon_data.get('name', 'Unknown')
                    print(f"         Slot {i+1}: #{species} -> #{adjusted_species} ({name})")
                else:
                    print(f"         Slot {i+1}: #{species} -> #{adjusted_species} (NÃO ENCONTRADO)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante verificação de offset: {e}")
        return False

def check_multiple_randomization_passes():
    """Verifica se há múltiplas passadas de randomização"""
    
    print(f"\n🔍 INVESTIGAÇÃO 7: Múltiplas Passadas de Randomização")
    print("-" * 50)
    
    print(f"   🎯 HIPÓTESE: Sistema faz múltiplas passadas:")
    print(f"      1. Expansão de party (adiciona slots)")
    print(f"      2. Randomização de adicionais (RANDOMIZE_ONLY_ADDITIONAL_POKEMON)")
    print(f"      3. Randomização geral (sobrescreve tudo?)")
    print(f"      4. Aplicação de temas (sobrescreve novamente?)")
    
    print(f"\n   🔍 EVIDÊNCIAS:")
    print(f"      - Logs mostram seleção correta (#23, #24)")
    print(f"      - Log final mostra outros IDs (#3, #643)")
    print(f"      - ROM final tem IDs completamente diferentes (#77, #58)")
    
    print(f"\n   💡 POSSÍVEL CAUSA:")
    print(f"      - Função de randomização geral ignora RANDOMIZE_ONLY_ADDITIONAL_POKEMON")
    print(f"      - Função de aplicação de temas sobrescreve seleções")
    print(f"      - Conversão incorreta entre Species ID e Pokédex Nacional")
    
    return True

if __name__ == "__main__":
    # Mudar para o diretório do projeto
    os.chdir("e:/Desktop/StarLyraFireRed/Dynamic-Pokemon-Expansion-Gen-9-master")
    
    # Executar investigações
    debug_id_confusion()
    check_rom_reading_offset()
    check_multiple_randomization_passes()
